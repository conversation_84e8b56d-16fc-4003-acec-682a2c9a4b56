#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版逐条对账程序
"""

import pandas as pd
import numpy as np

def main():
    try:
        print("=== 简化逐条对账程序 ===")
        
        # 1. 读取报表数据
        print("1. 读取报表数据...")
        报表数据 = pd.read_excel('7.04报表数.xlsx', sheet_name='Sheet1')
        报表数据['金额'] = pd.to_numeric(报表数据['fkje'], errors='coerce')
        报表数据 = 报表数据.dropna(subset=['金额'])
        print(f"   报表记录: {len(报表数据)}条")
        
        # 2. 读取农商行数据
        print("2. 读取农商行数据...")
        农商行原始 = pd.read_excel('农商行.xlsx', sheet_name='Sheet1')
        农商行数据 = 农商行原始.iloc[1:].reset_index(drop=True)
        农商行数据.columns = ['标识', '商户号', '营业名称', '交易时间', '终端号', '终端别名', 
                           '交易类型', '支付方式', '订单金额', '商户订单号', '支付流水号']
        农商行数据['金额'] = pd.to_numeric(农商行数据['订单金额'], errors='coerce')
        农商行数据 = 农商行数据.dropna(subset=['金额'])
        农商行数据 = 农商行数据[农商行数据['金额'] > 0]
        print(f"   农商行记录: {len(农商行数据)}条")
        
        # 3. 读取江苏行数据
        print("3. 读取江苏行数据...")
        江苏行数据 = pd.read_excel('江苏行.xlsx', sheet_name='江苏行')
        江苏行数据['金额'] = pd.to_numeric(江苏行数据['交易金额'], errors='coerce')
        江苏行数据 = 江苏行数据.dropna(subset=['金额'])
        print(f"   江苏行记录: {len(江苏行数据)}条")
        
        # 4. 创建金额统计
        print("\n4. 分析金额分布...")
        报表金额统计 = 报表数据['金额'].value_counts().sort_index()
        农商行金额统计 = 农商行数据['金额'].value_counts().sort_index()
        江苏行金额统计 = 江苏行数据['金额'].value_counts().sort_index()
        
        print(f"   报表不同金额种类: {len(报表金额统计)}")
        print(f"   农商行不同金额种类: {len(农商行金额统计)}")
        print(f"   江苏行不同金额种类: {len(江苏行金额统计)}")
        
        # 5. 逐条匹配分析
        print("\n5. 逐条匹配分析...")
        
        # 合并银行数据统计
        银行金额统计 = 农商行金额统计.add(江苏行金额统计, fill_value=0)
        
        # 找出不匹配的记录
        不匹配记录 = []
        匹配记录 = []
        
        for 金额, 报表数量 in 报表金额统计.items():
            银行数量 = int(银行金额统计.get(金额, 0))
            农商行数量 = 农商行金额统计.get(金额, 0)
            江苏行数量 = 江苏行金额统计.get(金额, 0)
            
            if 报表数量 != 银行数量:
                # 找出具体的不匹配记录
                报表记录 = 报表数据[报表数据['金额'] == 金额]
                
                for idx, 记录 in 报表记录.iterrows():
                    不匹配记录.append({
                        '金额': 金额,
                        '病人姓名': 记录.get('brxm', ''),
                        '付款名称': 记录.get('fkmc', ''),
                        '报表数量': 报表数量,
                        '银行总数量': 银行数量,
                        '农商行数量': 农商行数量,
                        '江苏行数量': 江苏行数量,
                        '差额': 报表数量 - 银行数量,
                        '状态': '数量不匹配' if 银行数量 > 0 else '银行无此金额'
                    })
            else:
                匹配记录.append({
                    '金额': 金额,
                    '数量': 报表数量
                })
        
        # 找出银行多余的记录
        银行多余记录 = []
        for 金额, 银行数量 in 银行金额统计.items():
            if 金额 not in 报表金额统计.index:
                # 这个金额在银行有但报表没有
                if 金额 in 农商行金额统计.index:
                    农商行记录 = 农商行数据[农商行数据['金额'] == 金额]
                    for idx, 记录 in 农商行记录.iterrows():
                        银行多余记录.append({
                            '金额': 金额,
                            '银行': '农商行',
                            '订单号': 记录.get('商户订单号', ''),
                            '交易时间': 记录.get('交易时间', ''),
                            '状态': '报表无此金额'
                        })
                
                if 金额 in 江苏行金额统计.index:
                    江苏行记录 = 江苏行数据[江苏行数据['金额'] == 金额]
                    for idx, 记录 in 江苏行记录.iterrows():
                        银行多余记录.append({
                            '金额': 金额,
                            '银行': '江苏行',
                            '订单号': 记录.get('商户订单号', ''),
                            '交易时间': 记录.get('交易时间', ''),
                            '状态': '报表无此金额'
                        })
        
        # 6. 输出结果
        print(f"\n=== 对账结果 ===")
        print(f"匹配成功的金额种类: {len(匹配记录)}")
        print(f"不匹配的记录: {len(不匹配记录)}条")
        print(f"银行多余的记录: {len(银行多余记录)}条")
        
        if 不匹配记录:
            print(f"\n不匹配记录明细 (前20条):")
            print(f"{'金额':>10} {'病人姓名':>10} {'付款名称':>15} {'报表':>4} {'银行':>4} {'状态':>10}")
            print("-" * 70)
            
            for 记录 in 不匹配记录[:20]:
                print(f"{记录['金额']:>10.2f} {记录['病人姓名']:>10} {记录['付款名称']:>15} "
                      f"{记录['报表数量']:>4} {记录['银行总数量']:>4} {记录['状态']:>10}")
        
        if 银行多余记录:
            print(f"\n银行多余记录明细 (前10条):")
            print(f"{'金额':>10} {'银行':>8} {'订单号':>25} {'状态':>10}")
            print("-" * 60)
            
            for 记录 in 银行多余记录[:10]:
                print(f"{记录['金额']:>10.2f} {记录['银行']:>8} {记录['订单号']:>25} {记录['状态']:>10}")
        
        # 7. 保存报告
        print(f"\n正在保存对账报告...")
        with pd.ExcelWriter('简化对账报告.xlsx', engine='openpyxl') as writer:
            if 不匹配记录:
                pd.DataFrame(不匹配记录).to_excel(writer, sheet_name='不匹配记录', index=False)
            if 银行多余记录:
                pd.DataFrame(银行多余记录).to_excel(writer, sheet_name='银行多余记录', index=False)
            if 匹配记录:
                pd.DataFrame(匹配记录).to_excel(writer, sheet_name='匹配记录', index=False)
        
        print("对账报告已保存为 '简化对账报告.xlsx'")
        
        # 8. 总结
        总记录数 = len(报表数据)
        不匹配数 = len(不匹配记录)
        匹配率 = ((总记录数 - 不匹配数) / 总记录数) * 100 if 总记录数 > 0 else 0
        
        print(f"\n=== 总结 ===")
        print(f"报表总记录: {总记录数}")
        print(f"不匹配记录: {不匹配数}")
        print(f"匹配率: {匹配率:.1f}%")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
