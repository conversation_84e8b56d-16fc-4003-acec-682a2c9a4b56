#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证特殊记录 - 检查3.6元和3.3元记录的真实匹配情况
"""

import pandas as pd

def load_data():
    """加载数据"""
    print("=== 加载数据 ===")
    
    # 1. 报表数据
    报表数据 = pd.read_excel('7.04报表数.xlsx', sheet_name='Sheet1')
    报表数据['金额'] = pd.to_numeric(报表数据['fkje'], errors='coerce')
    报表数据 = 报表数据.dropna(subset=['金额'])
    print(f"报表数据: {len(报表数据)}条")
    
    # 2. 农商行数据
    农商行原始 = pd.read_excel('农商行.xlsx', sheet_name='Sheet1')
    农商行数据 = 农商行原始.iloc[1:].reset_index(drop=True)
    农商行数据.columns = ['标识', '商户号', '营业名称', '交易时间', '终端号', '终端别名', 
                       '交易类型', '支付方式', '订单金额', '商户订单号', '支付流水号']
    农商行数据['金额'] = pd.to_numeric(农商行数据['订单金额'], errors='coerce')
    农商行数据 = 农商行数据.dropna(subset=['金额'])
    农商行数据 = 农商行数据[农商行数据['金额'] > 0]
    print(f"农商行数据: {len(农商行数据)}条")
    
    # 3. 江苏行数据
    江苏行数据 = pd.read_excel('江苏行.xlsx', sheet_name='江苏行')
    江苏行数据['金额'] = pd.to_numeric(江苏行数据['交易金额'], errors='coerce')
    江苏行数据 = 江苏行数据.dropna(subset=['金额'])
    江苏行正数 = 江苏行数据[江苏行数据['金额'] > 0].copy()
    print(f"江苏行数据(正数): {len(江苏行正数)}条")
    
    return 报表数据, 农商行数据, 江苏行正数

def verify_special_records():
    """验证特殊记录"""
    print("\n=== 验证特殊记录 ===")
    
    报表数据, 农商行数据, 江苏行数据 = load_data()
    银行数据 = pd.concat([农商行数据, 江苏行数据], ignore_index=True)
    
    # 特殊关注的订单号
    特殊订单号 = [
        'S202507041353301001077384',  # 3.6元
        'S202507041610531001121128'   # 3.3元
    ]
    
    for 订单号 in 特殊订单号:
        print(f"\n=== 分析订单号: {订单号} ===")
        
        # 1. 在银行数据中查找
        银行记录 = 银行数据[银行数据['商户订单号'].astype(str) == 订单号]
        
        if len(银行记录) > 0:
            银行记录_详情 = 银行记录.iloc[0]
            print(f"银行记录:")
            print(f"  金额: {银行记录_详情['金额']}元")
            print(f"  银行来源: {银行记录_详情.get('银行来源', '未知')}")
            print(f"  交易时间: {银行记录_详情.get('交易时间', '未知')}")
            
            # 2. 在报表数据中查找包含此订单号的记录
            print(f"\n报表中包含此订单号的记录:")
            包含订单号的报表 = 报表数据[报表数据['yhddh'].astype(str).str.contains(订单号, na=False)]
            
            if len(包含订单号的报表) > 0:
                print(f"  找到 {len(包含订单号的报表)} 条包含此订单号的报表记录:")
                for idx, 报表记录 in 包含订单号的报表.iterrows():
                    print(f"    病人: {报表记录.get('brxm', '')}")
                    print(f"    金额: {报表记录['金额']}元")
                    print(f"    订单号: {报表记录.get('yhddh', '')}")
                    print(f"    费用名称: {报表记录.get('fkmc', '')}")
                    print(f"    ---")
                
                # 检查金额是否匹配
                报表金额总和 = 包含订单号的报表['金额'].sum()
                print(f"  报表金额总和: {报表金额总和}元")
                print(f"  银行金额: {银行记录_详情['金额']}元")
                print(f"  金额差异: {abs(报表金额总和 - 银行记录_详情['金额'])}")
                
                if abs(报表金额总和 - 银行记录_详情['金额']) < 0.01:
                    print(f"  ✅ 金额匹配！这不是单边数据")
                else:
                    print(f"  ❌ 金额不匹配！可能是单边数据")
            else:
                print(f"  ❌ 报表中没有找到包含此订单号的记录！这是真正的银行单边数据")
            
            # 3. 查找相同金额的报表记录
            print(f"\n报表中相同金额({银行记录_详情['金额']}元)的所有记录:")
            相同金额报表 = 报表数据[报表数据['金额'] == 银行记录_详情['金额']]
            print(f"  共找到 {len(相同金额报表)} 条相同金额的报表记录:")
            
            for idx, 报表记录 in 相同金额报表.iterrows():
                print(f"    病人: {报表记录.get('brxm', '')}")
                print(f"    订单号: {报表记录.get('yhddh', '')}")
                print(f"    费用名称: {报表记录.get('fkmc', '')}")
            
            # 4. 查找相同金额的银行记录
            print(f"\n银行中相同金额({银行记录_详情['金额']}元)的所有记录:")
            相同金额银行 = 银行数据[银行数据['金额'] == 银行记录_详情['金额']]
            print(f"  共找到 {len(相同金额银行)} 条相同金额的银行记录:")
            
            for idx, 银行记录_item in 相同金额银行.iterrows():
                print(f"    订单号: {银行记录_item.get('商户订单号', '')}")
                print(f"    银行来源: {银行记录_item.get('银行来源', '')}")
                print(f"    交易时间: {银行记录_item.get('交易时间', '')}")
            
            # 5. 数量对比分析
            print(f"\n数量对比分析:")
            print(f"  报表中{银行记录_详情['金额']}元记录数: {len(相同金额报表)}")
            print(f"  银行中{银行记录_详情['金额']}元记录数: {len(相同金额银行)}")
            print(f"  数量差异: {len(相同金额银行) - len(相同金额报表)}")
            
            if len(相同金额银行) > len(相同金额报表):
                print(f"  ⚠️  银行记录比报表记录多 {len(相同金额银行) - len(相同金额报表)} 条")
                print(f"  这意味着有 {len(相同金额银行) - len(相同金额报表)} 条银行单边数据")
            elif len(相同金额银行) < len(相同金额报表):
                print(f"  ⚠️  报表记录比银行记录多 {len(相同金额报表) - len(相同金额银行)} 条")
            else:
                print(f"  ✅ 数量完全匹配")
        
        else:
            print(f"❌ 在银行数据中没有找到订单号 {订单号}")

def main():
    """主函数"""
    try:
        verify_special_records()
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
