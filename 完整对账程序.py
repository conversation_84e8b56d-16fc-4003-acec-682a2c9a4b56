#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整对账程序 - 四层优先级匹配策略
1. 订单号精确匹配
2. 金额直接匹配  
3. 合并支付检测
4. 净额匹配（退款处理）
"""

import pandas as pd
import numpy as np
from itertools import combinations
import re

def load_data():
    """加载数据"""
    print("=== 加载数据 ===")
    
    # 1. 报表数据
    报表数据 = pd.read_excel('7.04报表数.xlsx', sheet_name='Sheet1')
    报表数据['金额'] = pd.to_numeric(报表数据['fkje'], errors='coerce')
    报表数据 = 报表数据.dropna(subset=['金额'])
    报表数据['已匹配'] = False
    报表数据['匹配类型'] = ''
    报表数据['匹配详情'] = ''
    print(f"报表数据: {len(报表数据)}条")
    
    # 2. 农商行数据
    农商行原始 = pd.read_excel('农商行.xlsx', sheet_name='Sheet1')
    农商行数据 = 农商行原始.iloc[1:].reset_index(drop=True)
    农商行数据.columns = ['标识', '商户号', '营业名称', '交易时间', '终端号', '终端别名', 
                       '交易类型', '支付方式', '订单金额', '商户订单号', '支付流水号']
    农商行数据['金额'] = pd.to_numeric(农商行数据['订单金额'], errors='coerce')
    农商行数据 = 农商行数据.dropna(subset=['金额'])
    农商行数据 = 农商行数据[农商行数据['金额'] > 0]
    农商行数据['银行来源'] = '农商行'
    农商行数据['已匹配'] = False
    print(f"农商行数据: {len(农商行数据)}条")
    
    # 3. 江苏行数据
    江苏行数据 = pd.read_excel('江苏行.xlsx', sheet_name='江苏行')
    江苏行数据['金额'] = pd.to_numeric(江苏行数据['交易金额'], errors='coerce')
    江苏行数据 = 江苏行数据.dropna(subset=['金额'])
    江苏行正数 = 江苏行数据[江苏行数据['金额'] > 0].copy()
    江苏行正数['银行来源'] = '江苏行'
    江苏行正数['已匹配'] = False
    print(f"江苏行数据(正数): {len(江苏行正数)}条")
    
    return 报表数据, 农商行数据, 江苏行正数

def layer1_order_number_match(报表数据, 银行数据):
    """第一层：订单号精确匹配"""
    print("\n=== 第一层：订单号精确匹配 ===")
    
    匹配结果 = []
    匹配计数 = 0
    
    for 银行idx, 银行记录 in 银行数据.iterrows():
        if 银行记录['已匹配']:
            continue
            
        银行订单号 = str(银行记录.get('商户订单号', ''))
        if not 银行订单号 or 银行订单号 == 'nan':
            continue
        
        # 在报表的yhddh字段中查找匹配
        报表匹配 = 报表数据[
            (报表数据['已匹配'] == False) & 
            (报表数据['yhddh'].str.contains(银行订单号, na=False, regex=False))
        ]
        
        if len(报表匹配) > 0:
            # 可能有多个报表记录对应一个银行订单号（合并支付）
            报表金额总和 = 报表匹配['金额'].sum()
            银行金额 = 银行记录['金额']
            
            if abs(报表金额总和 - 银行金额) < 0.01:
                # 金额匹配，标记所有相关记录
                for 报表idx in 报表匹配.index:
                    报表数据.loc[报表idx, '已匹配'] = True
                    报表数据.loc[报表idx, '匹配类型'] = '订单号匹配'
                    报表数据.loc[报表idx, '匹配详情'] = f"银行订单号:{银行订单号}"
                
                银行数据.loc[银行idx, '已匹配'] = True
                
                匹配结果.append({
                    '匹配类型': '订单号匹配',
                    '银行金额': 银行金额,
                    '报表金额': 报表金额总和,
                    '银行来源': 银行记录['银行来源'],
                    '银行订单号': 银行订单号,
                    '报表记录数': len(报表匹配),
                    '病人姓名': ','.join(报表匹配['brxm'].unique())
                })
                
                匹配计数 += len(报表匹配)
    
    print(f"订单号匹配成功: {len(匹配结果)}笔银行记录, {匹配计数}条报表记录")
    return 匹配结果

def layer2_direct_amount_match(报表数据, 银行数据):
    """第二层：金额直接匹配"""
    print("\n=== 第二层：金额直接匹配 ===")
    
    匹配结果 = []
    
    for 银行idx, 银行记录 in 银行数据.iterrows():
        if 银行记录['已匹配']:
            continue
            
        银行金额 = 银行记录['金额']
        
        # 查找相同金额的未匹配报表记录
        报表匹配 = 报表数据[
            (报表数据['已匹配'] == False) & 
            (报表数据['金额'] == 银行金额)
        ]
        
        if len(报表匹配) > 0:
            # 取第一个匹配的记录
            报表记录 = 报表匹配.iloc[0]
            
            # 标记为已匹配
            报表数据.loc[报表记录.name, '已匹配'] = True
            报表数据.loc[报表记录.name, '匹配类型'] = '金额直接匹配'
            报表数据.loc[报表记录.name, '匹配详情'] = f"金额:{银行金额}"
            
            银行数据.loc[银行idx, '已匹配'] = True
            
            匹配结果.append({
                '匹配类型': '金额直接匹配',
                '银行金额': 银行金额,
                '报表金额': 报表记录['金额'],
                '银行来源': 银行记录['银行来源'],
                '银行订单号': 银行记录.get('商户订单号', ''),
                '病人姓名': 报表记录.get('brxm', '')
            })
    
    print(f"金额直接匹配成功: {len(匹配结果)}笔")
    return 匹配结果

def layer3_merged_payment_match(报表数据, 银行数据):
    """第三层：合并支付检测"""
    print("\n=== 第三层：合并支付检测 ===")
    
    匹配结果 = []
    
    # 获取未匹配的正数报表记录，按病人分组
    未匹配报表 = 报表数据[(报表数据['已匹配'] == False) & (报表数据['金额'] > 0)]
    报表按病人分组 = 未匹配报表.groupby('brxm')
    
    for 银行idx, 银行记录 in 银行数据.iterrows():
        if 银行记录['已匹配']:
            continue
            
        银行金额 = 银行记录['金额']
        找到匹配 = False
        
        # 在每个病人的记录中寻找组合匹配
        for 病人姓名, 病人记录组 in 报表按病人分组:
            if 找到匹配:
                break
                
            病人未匹配记录 = 病人记录组[病人记录组['已匹配'] == False]
            
            if len(病人未匹配记录) < 2:
                continue
            
            金额列表 = 病人未匹配记录['金额'].tolist()
            
            # 尝试2-5个金额的组合
            for 组合数 in range(2, min(6, len(金额列表) + 1)):
                for 组合 in combinations(金额列表, 组合数):
                    if abs(sum(组合) - 银行金额) < 0.01:
                        # 找到匹配组合，标记相关记录
                        组合记录索引 = []
                        剩余金额 = list(组合)
                        
                        for idx, 记录 in 病人未匹配记录.iterrows():
                            if 记录['金额'] in 剩余金额:
                                组合记录索引.append(idx)
                                剩余金额.remove(记录['金额'])
                                if len(剩余金额) == 0:
                                    break
                        
                        if len(组合记录索引) == len(组合):
                            # 标记为已匹配
                            for 记录idx in 组合记录索引:
                                报表数据.loc[记录idx, '已匹配'] = True
                                报表数据.loc[记录idx, '匹配类型'] = '合并支付'
                                报表数据.loc[记录idx, '匹配详情'] = f"病人:{病人姓名},组合:{'+'.join([str(x) for x in 组合])}"
                            
                            银行数据.loc[银行idx, '已匹配'] = True
                            
                            匹配结果.append({
                                '匹配类型': '合并支付',
                                '银行金额': 银行金额,
                                '报表金额': sum(组合),
                                '银行来源': 银行记录['银行来源'],
                                '银行订单号': 银行记录.get('商户订单号', ''),
                                '病人姓名': 病人姓名,
                                '组合明细': f"{'+'.join([str(x) for x in 组合])}",
                                '组合数量': len(组合)
                            })
                            
                            找到匹配 = True
                            break
                    if 找到匹配:
                        break
                if 找到匹配:
                    break
    
    print(f"合并支付匹配成功: {len(匹配结果)}笔")
    return 匹配结果

def layer4_net_amount_match(报表数据, 银行数据):
    """第四层：净额匹配（退款处理）"""
    print("\n=== 第四层：净额匹配（退款处理） ===")
    
    匹配结果 = []
    
    # 获取未匹配的报表记录，按病人分组
    未匹配报表 = 报表数据[报表数据['已匹配'] == False]
    报表按病人分组 = 未匹配报表.groupby('brxm')
    
    for 银行idx, 银行记录 in 银行数据.iterrows():
        if 银行记录['已匹配']:
            continue
            
        银行金额 = 银行记录['金额']
        找到匹配 = False
        
        # 在每个病人的记录中寻找净额匹配
        for 病人姓名, 病人记录组 in 报表按病人分组:
            if 找到匹配:
                break
                
            病人未匹配记录 = 病人记录组[病人记录组['已匹配'] == False]
            
            if len(病人未匹配记录) < 2:
                continue
            
            金额列表 = 病人未匹配记录['金额'].tolist()
            
            # 尝试包含正负金额的组合
            for 组合数 in range(2, min(6, len(金额列表) + 1)):
                for 组合 in combinations(金额列表, 组合数):
                    净额 = sum(组合)
                    if abs(净额 - 银行金额) < 0.01:
                        # 检查是否包含负数（退款）
                        负数金额 = [x for x in 组合 if x < 0]
                        正数金额 = [x for x in 组合 if x > 0]
                        
                        if len(负数金额) > 0:  # 确实包含退款
                            # 找到匹配组合，标记相关记录
                            组合记录索引 = []
                            剩余金额 = list(组合)
                            
                            for idx, 记录 in 病人未匹配记录.iterrows():
                                if 记录['金额'] in 剩余金额:
                                    组合记录索引.append(idx)
                                    剩余金额.remove(记录['金额'])
                                    if len(剩余金额) == 0:
                                        break
                            
                            if len(组合记录索引) == len(组合):
                                # 标记为已匹配
                                for 记录idx in 组合记录索引:
                                    报表数据.loc[记录idx, '已匹配'] = True
                                    报表数据.loc[记录idx, '匹配类型'] = '净额匹配'
                                    报表数据.loc[记录idx, '匹配详情'] = f"病人:{病人姓名},净额:{净额}"
                                
                                银行数据.loc[银行idx, '已匹配'] = True
                                
                                匹配结果.append({
                                    '匹配类型': '净额匹配',
                                    '银行金额': 银行金额,
                                    '报表净额': 净额,
                                    '银行来源': 银行记录['银行来源'],
                                    '银行订单号': 银行记录.get('商户订单号', ''),
                                    '病人姓名': 病人姓名,
                                    '正数金额': 正数金额,
                                    '负数金额': 负数金额,
                                    '组合明细': f"{'+'.join([str(x) for x in 组合])}"
                                })
                                
                                找到匹配 = True
                                break
                    if 找到匹配:
                        break
                if 找到匹配:
                    break
    
    print(f"净额匹配成功: {len(匹配结果)}笔")
    return 匹配结果

def analyze_unmatched_records(报表数据, 银行数据):
    """分析最终未匹配的记录"""
    print("\n=== 分析最终未匹配记录 ===")
    
    未匹配报表 = 报表数据[报表数据['已匹配'] == False]
    未匹配银行 = 银行数据[银行数据['已匹配'] == False]
    
    print(f"最终未匹配报表记录: {len(未匹配报表)}条")
    print(f"最终未匹配银行记录: {len(未匹配银行)}条")
    
    # 分析未匹配报表记录
    if len(未匹配报表) > 0:
        负数记录 = 未匹配报表[未匹配报表['金额'] < 0]
        正数记录 = 未匹配报表[未匹配报表['金额'] > 0]
        
        print(f"  未匹配报表负数记录: {len(负数记录)}条")
        print(f"  未匹配报表正数记录: {len(正数记录)}条")
        
        if len(负数记录) > 0:
            print(f"  负数记录示例:")
            for idx, 记录 in 负数记录.head(5).iterrows():
                print(f"    病人:{记录.get('brxm', '')}, 金额:{记录['金额']}")
        
        if len(正数记录) > 0:
            print(f"  正数记录示例:")
            for idx, 记录 in 正数记录.head(5).iterrows():
                print(f"    病人:{记录.get('brxm', '')}, 金额:{记录['金额']}")
    
    # 分析未匹配银行记录
    if len(未匹配银行) > 0:
        print(f"  未匹配银行记录示例:")
        for idx, 记录 in 未匹配银行.head(5).iterrows():
            print(f"    {记录['银行来源']}, 金额:{记录['金额']}, 订单号:{记录.get('商户订单号', '')}")
    
    return 未匹配报表, 未匹配银行

def main():
    """主函数"""
    try:
        print("=== 完整对账程序 ===")
        
        # 加载数据
        报表数据, 农商行数据, 江苏行数据 = load_data()
        
        # 合并银行数据
        银行数据 = pd.concat([农商行数据, 江苏行数据], ignore_index=True)
        print(f"银行总数据: {len(银行数据)}条")
        
        # 四层匹配
        订单号匹配结果 = layer1_order_number_match(报表数据, 银行数据)
        金额匹配结果 = layer2_direct_amount_match(报表数据, 银行数据)
        合并支付结果 = layer3_merged_payment_match(报表数据, 银行数据)
        净额匹配结果 = layer4_net_amount_match(报表数据, 银行数据)
        
        # 分析未匹配记录
        未匹配报表, 未匹配银行 = analyze_unmatched_records(报表数据, 银行数据)
        
        # 统计结果
        总匹配数 = len(订单号匹配结果) + len(金额匹配结果) + len(合并支付结果) + len(净额匹配结果)
        匹配率 = (len(银行数据) - len(未匹配银行)) / len(银行数据) * 100
        
        print(f"\n=== 最终统计结果 ===")
        print(f"订单号匹配: {len(订单号匹配结果)}笔")
        print(f"金额直接匹配: {len(金额匹配结果)}笔")
        print(f"合并支付匹配: {len(合并支付结果)}笔")
        print(f"净额匹配: {len(净额匹配结果)}笔")
        print(f"总匹配数: {总匹配数}笔")
        print(f"银行记录匹配率: {匹配率:.1f}%")
        
        # 保存详细报告
        print(f"\n正在生成完整对账报告...")
        with pd.ExcelWriter('完整对账报告.xlsx', engine='openpyxl') as writer:
            # 各层匹配结果
            if 订单号匹配结果:
                pd.DataFrame(订单号匹配结果).to_excel(writer, sheet_name='订单号匹配', index=False)
            if 金额匹配结果:
                pd.DataFrame(金额匹配结果).to_excel(writer, sheet_name='金额直接匹配', index=False)
            if 合并支付结果:
                pd.DataFrame(合并支付结果).to_excel(writer, sheet_name='合并支付匹配', index=False)
            if 净额匹配结果:
                pd.DataFrame(净额匹配结果).to_excel(writer, sheet_name='净额匹配', index=False)
            
            # 未匹配记录
            if len(未匹配报表) > 0:
                未匹配报表[['brxm', 'fkje', 'fkmc', 'yhddh', '匹配类型']].to_excel(
                    writer, sheet_name='未匹配报表', index=False)
            if len(未匹配银行) > 0:
                未匹配银行[['金额', '银行来源', '商户订单号', '交易时间']].to_excel(
                    writer, sheet_name='未匹配银行', index=False)
            
            # 汇总统计
            汇总数据 = pd.DataFrame({
                '匹配类型': ['订单号匹配', '金额直接匹配', '合并支付匹配', '净额匹配', '未匹配'],
                '数量': [len(订单号匹配结果), len(金额匹配结果), len(合并支付结果), 
                        len(净额匹配结果), len(未匹配银行)]
            })
            汇总数据.to_excel(writer, sheet_name='汇总统计', index=False)
        
        print("完整对账报告已保存为 '完整对账报告.xlsx'")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
