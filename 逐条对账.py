#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
逐条对账程序 - 精确匹配每一笔交易记录
"""

import pandas as pd
import numpy as np

def load_and_clean_data():
    """加载并清理所有数据"""
    print("加载数据...")
    
    # 1. 读取报表数据
    报表数据 = pd.read_excel('7.04报表数.xlsx', sheet_name='Sheet1')
    报表数据['金额'] = pd.to_numeric(报表数据['fkje'], errors='coerce')
    报表数据 = 报表数据.dropna(subset=['金额'])
    报表数据['数据源'] = '报表'
    报表数据['记录ID'] = '报表_' + 报表数据.index.astype(str)
    
    # 2. 读取农商行数据
    农商行原始 = pd.read_excel('农商行.xlsx', sheet_name='Sheet1')
    农商行数据 = 农商行原始.iloc[1:].reset_index(drop=True)  # 跳过标题行
    农商行数据.columns = ['标识', '商户号', '营业名称', '交易时间', '终端号', '终端别名', 
                       '交易类型', '支付方式', '订单金额', '商户订单号', '支付流水号']
    农商行数据['金额'] = pd.to_numeric(农商行数据['订单金额'], errors='coerce')
    农商行数据 = 农商行数据.dropna(subset=['金额'])
    农商行数据 = 农商行数据[农商行数据['金额'] > 0]
    农商行数据['数据源'] = '农商行'
    农商行数据['记录ID'] = '农商行_' + 农商行数据.index.astype(str)
    
    # 3. 读取江苏行数据
    江苏行数据 = pd.read_excel('江苏行.xlsx', sheet_name='江苏行')
    江苏行数据['金额'] = pd.to_numeric(江苏行数据['交易金额'], errors='coerce')
    江苏行数据 = 江苏行数据.dropna(subset=['金额'])
    江苏行数据['数据源'] = '江苏行'
    江苏行数据['记录ID'] = '江苏行_' + 江苏行数据.index.astype(str)
    
    print(f"报表数据: {len(报表数据)}条")
    print(f"农商行数据: {len(农商行数据)}条")
    print(f"江苏行数据: {len(江苏行数据)}条")
    
    return 报表数据, 农商行数据, 江苏行数据

def match_records_by_amount_and_order(报表数据, 银行数据, 银行名称):
    """
    按金额和订单号匹配记录
    """
    print(f"\n正在匹配 {银行名称} 数据...")
    
    匹配结果 = []
    未匹配报表 = []
    未匹配银行 = []
    
    # 创建银行数据的副本用于匹配
    银行数据_copy = 银行数据.copy()
    银行数据_copy['已匹配'] = False
    
    # 为每条报表记录寻找匹配
    for idx, 报表记录 in 报表数据.iterrows():
        报表金额 = 报表记录['金额']
        
        # 在银行数据中寻找相同金额的记录
        候选匹配 = 银行数据_copy[
            (银行数据_copy['金额'] == 报表金额) & 
            (银行数据_copy['已匹配'] == False)
        ]
        
        if len(候选匹配) > 0:
            # 找到匹配，取第一个
            匹配记录 = 候选匹配.iloc[0]
            
            匹配结果.append({
                '报表记录ID': 报表记录['记录ID'],
                '银行记录ID': 匹配记录['记录ID'],
                '金额': 报表金额,
                '报表病人姓名': 报表记录.get('brxm', ''),
                '报表付款名称': 报表记录.get('fkmc', ''),
                '银行订单号': 匹配记录.get('商户订单号', 匹配记录.get('商户订单号', '')),
                '匹配状态': '已匹配'
            })
            
            # 标记为已匹配
            银行数据_copy.loc[匹配记录.name, '已匹配'] = True
        else:
            # 未找到匹配
            未匹配报表.append({
                '记录ID': 报表记录['记录ID'],
                '金额': 报表金额,
                '病人姓名': 报表记录.get('brxm', ''),
                '付款名称': 报表记录.get('fkmc', ''),
                '原因': f'在{银行名称}中未找到相同金额的记录'
            })
    
    # 收集未匹配的银行记录
    未匹配银行记录 = 银行数据_copy[银行数据_copy['已匹配'] == False]
    for idx, 银行记录 in 未匹配银行记录.iterrows():
        未匹配银行.append({
            '记录ID': 银行记录['记录ID'],
            '金额': 银行记录['金额'],
            '订单号': 银行记录.get('商户订单号', 银行记录.get('商户订单号', '')),
            '原因': '在报表中未找到相同金额的记录'
        })
    
    return 匹配结果, 未匹配报表, 未匹配银行

def perform_detailed_reconciliation():
    """执行详细的逐条对账"""
    try:
        print("=== 逐条对账程序 ===")
        
        # 加载数据
        报表数据, 农商行数据, 江苏行数据 = load_and_clean_data()
        
        # 分别与两个银行进行匹配
        农商行匹配, 农商行未匹配报表, 农商行未匹配银行 = match_records_by_amount_and_order(
            报表数据, 农商行数据, "农商行"
        )
        
        江苏行匹配, 江苏行未匹配报表, 江苏行未匹配银行 = match_records_by_amount_and_order(
            报表数据, 江苏行数据, "江苏行"
        )
        
        # 统计结果
        print(f"\n=== 匹配统计 ===")
        print(f"农商行匹配成功: {len(农商行匹配)}条")
        print(f"农商行未匹配报表记录: {len(农商行未匹配报表)}条")
        print(f"农商行未匹配银行记录: {len(农商行未匹配银行)}条")
        
        print(f"江苏行匹配成功: {len(江苏行匹配)}条")
        print(f"江苏行未匹配报表记录: {len(江苏行未匹配报表)}条")
        print(f"江苏行未匹配银行记录: {len(江苏行未匹配银行)}条")
        
        # 合并所有未匹配记录
        所有未匹配报表 = []
        
        # 找出在两个银行都未匹配的报表记录
        农商行未匹配ID = {item['记录ID'] for item in 农商行未匹配报表}
        江苏行未匹配ID = {item['记录ID'] for item in 江苏行未匹配报表}
        
        # 在两个银行都未找到的记录
        两银行都未匹配 = 农商行未匹配ID & 江苏行未匹配ID
        
        # 只在一个银行未匹配的记录
        仅农商行未匹配 = 农商行未匹配ID - 江苏行未匹配ID
        仅江苏行未匹配 = 江苏行未匹配ID - 农商行未匹配ID
        
        print(f"\n=== 详细分析 ===")
        print(f"两个银行都未找到匹配: {len(两银行都未匹配)}条")
        print(f"仅在农商行未找到匹配: {len(仅农商行未匹配)}条")
        print(f"仅在江苏行未找到匹配: {len(仅江苏行未匹配)}条")
        
        # 显示未匹配明细
        if 两银行都未匹配:
            print(f"\n两个银行都未找到匹配的报表记录:")
            for item in 农商行未匹配报表:
                if item['记录ID'] in 两银行都未匹配:
                    print(f"  金额: {item['金额']:>8.2f}, 病人: {item['病人姓名']}, 付款名称: {item['付款名称']}")
        
        if 仅农商行未匹配:
            print(f"\n仅在农商行未找到匹配的报表记录:")
            for item in 农商行未匹配报表:
                if item['记录ID'] in 仅农商行未匹配:
                    print(f"  金额: {item['金额']:>8.2f}, 病人: {item['病人姓名']}, 付款名称: {item['付款名称']}")
        
        if 仅江苏行未匹配:
            print(f"\n仅在江苏行未找到匹配的报表记录:")
            for item in 江苏行未匹配报表:
                if item['记录ID'] in 仅江苏行未匹配:
                    print(f"  金额: {item['金额']:>8.2f}, 病人: {item['病人姓名']}, 付款名称: {item['付款名称']}")
        
        # 显示银行中多余的记录
        if 农商行未匹配银行:
            print(f"\n农商行中未在报表找到匹配的记录 (前10条):")
            for item in 农商行未匹配银行[:10]:
                print(f"  金额: {item['金额']:>8.2f}, 订单号: {item['订单号']}")
        
        if 江苏行未匹配银行:
            print(f"\n江苏行中未在报表找到匹配的记录 (前10条):")
            for item in 江苏行未匹配银行[:10]:
                print(f"  金额: {item['金额']:>8.2f}, 订单号: {item['订单号']}")
        
        # 保存详细报告
        print(f"\n正在生成逐条对账报告...")
        with pd.ExcelWriter('逐条对账报告.xlsx', engine='openpyxl') as writer:
            # 匹配成功的记录
            if 农商行匹配:
                pd.DataFrame(农商行匹配).to_excel(writer, sheet_name='农商行匹配记录', index=False)
            if 江苏行匹配:
                pd.DataFrame(江苏行匹配).to_excel(writer, sheet_name='江苏行匹配记录', index=False)
            
            # 未匹配的报表记录
            if 农商行未匹配报表:
                pd.DataFrame(农商行未匹配报表).to_excel(writer, sheet_name='农商行未匹配报表', index=False)
            if 江苏行未匹配报表:
                pd.DataFrame(江苏行未匹配报表).to_excel(writer, sheet_name='江苏行未匹配报表', index=False)
            
            # 未匹配的银行记录
            if 农商行未匹配银行:
                pd.DataFrame(农商行未匹配银行).to_excel(writer, sheet_name='农商行多余记录', index=False)
            if 江苏行未匹配银行:
                pd.DataFrame(江苏行未匹配银行).to_excel(writer, sheet_name='江苏行多余记录', index=False)
        
        print("逐条对账报告已保存为 '逐条对账报告.xlsx'")
        
        # 总结
        总报表记录 = len(报表数据)
        总匹配记录 = len(农商行匹配) + len(江苏行匹配)
        总未匹配记录 = len(两银行都未匹配) + len(仅农商行未匹配) + len(仅江苏行未匹配)
        
        print(f"\n=== 对账总结 ===")
        print(f"报表总记录数: {总报表记录}")
        print(f"成功匹配记录: {总匹配记录}")
        print(f"未匹配记录: {总未匹配记录}")
        print(f"匹配率: {(总匹配记录/总报表记录)*100:.1f}%")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    perform_detailed_reconciliation()
