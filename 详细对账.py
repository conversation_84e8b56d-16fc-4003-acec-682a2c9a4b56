#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
详细对账程序 - 修复农商行数据解析问题
"""

import pandas as pd
import numpy as np

def parse_nongshang_data():
    """解析农商行数据"""
    print("详细解析农商行数据...")
    
    # 读取原始数据
    农商行原始 = pd.read_excel('农商行.xlsx', sheet_name='Sheet1')
    
    # 查看前几行来理解数据结构
    print("农商行数据前10行:")
    for i in range(min(10, len(农商行原始))):
        print(f"第{i+1}行: {农商行原始.iloc[i].values}")
    
    # 寻找包含实际数据的行
    data_start_row = None
    for i in range(len(农商行原始)):
        row_values = 农商行原始.iloc[i].values
        # 检查是否包含数字金额
        for val in row_values:
            if isinstance(val, (int, float)) and val > 0:
                data_start_row = i
                break
        if data_start_row is not None:
            break
    
    if data_start_row is not None:
        print(f"找到农商行数据开始行: {data_start_row + 1}")
        农商行数据 = 农商行原始.iloc[data_start_row:].reset_index(drop=True)
        
        # 查找金额列
        amount_col = None
        for col_idx, col in enumerate(农商行数据.columns):
            try:
                # 尝试转换为数字并检查是否有合理的金额值
                numeric_vals = pd.to_numeric(农商行数据.iloc[:, col_idx], errors='coerce')
                if numeric_vals.notna().sum() > 0 and (numeric_vals > 0).sum() > 0:
                    amount_col = col_idx
                    print(f"找到疑似金额列: 第{col_idx+1}列")
                    break
            except:
                continue
        
        if amount_col is not None:
            农商行数据['金额'] = pd.to_numeric(农商行数据.iloc[:, amount_col], errors='coerce')
            农商行数据 = 农商行数据.dropna(subset=['金额'])
            农商行数据 = 农商行数据[农商行数据['金额'] > 0]  # 只保留正数金额
            return 农商行数据
    
    return pd.DataFrame()

def detailed_reconciliation():
    """执行详细对账"""
    try:
        print("=== 详细对账程序 ===\n")
        
        # 1. 读取报表数据
        print("1. 读取报表数据...")
        报表数据 = pd.read_excel('7.04报表数.xlsx', sheet_name='Sheet1')
        报表数据['金额'] = pd.to_numeric(报表数据['fkje'], errors='coerce')
        报表数据 = 报表数据.dropna(subset=['金额'])
        报表总金额 = 报表数据['金额'].sum()
        print(f"   报表有效记录: {len(报表数据)}条")
        print(f"   报表总金额: {报表总金额:,.2f}")
        
        # 2. 解析农商行数据
        print("\n2. 解析农商行数据...")
        农商行数据 = parse_nongshang_data()
        if len(农商行数据) > 0:
            农商行总金额 = 农商行数据['金额'].sum()
            print(f"   农商行有效记录: {len(农商行数据)}条")
            print(f"   农商行总金额: {农商行总金额:,.2f}")
        else:
            农商行总金额 = 0
            print("   农商行数据解析失败")
        
        # 3. 读取江苏行数据
        print("\n3. 读取江苏行数据...")
        江苏行数据 = pd.read_excel('江苏行.xlsx', sheet_name='江苏行')
        江苏行数据['金额'] = pd.to_numeric(江苏行数据['交易金额'], errors='coerce')
        江苏行数据 = 江苏行数据.dropna(subset=['金额'])
        江苏行总金额 = 江苏行数据['金额'].sum()
        print(f"   江苏行有效记录: {len(江苏行数据)}条")
        print(f"   江苏行总金额: {江苏行总金额:,.2f}")
        
        # 4. 对账汇总
        银行总金额 = 农商行总金额 + 江苏行总金额
        差额 = 报表总金额 - 银行总金额
        
        print(f"\n=== 对账汇总结果 ===")
        print(f"报表总金额:      {报表总金额:>15,.2f}")
        print(f"农商行总金额:    {农商行总金额:>15,.2f}")
        print(f"江苏行总金额:    {江苏行总金额:>15,.2f}")
        print(f"银行合计金额:    {银行总金额:>15,.2f}")
        print(f"差额:            {差额:>15,.2f}")
        
        # 5. 分析不匹配情况
        if abs(差额) > 0.01:
            print(f"\n=== 不匹配分析 ===")
            
            # 创建金额对比表
            def create_amount_summary(df, name):
                if len(df) > 0:
                    summary = df['金额'].value_counts().sort_index()
                    return summary
                return pd.Series()
            
            报表金额统计 = create_amount_summary(报表数据, "报表")
            农商行金额统计 = create_amount_summary(农商行数据, "农商行")
            江苏行金额统计 = create_amount_summary(江苏行数据, "江苏行")
            
            # 合并银行统计
            银行金额统计 = 农商行金额统计.add(江苏行金额统计, fill_value=0)
            
            # 找出不匹配的金额
            all_amounts = set(报表金额统计.index) | set(银行金额统计.index)
            
            不匹配明细 = []
            for amount in sorted(all_amounts):
                报表count = 报表金额统计.get(amount, 0)
                银行count = 银行金额统计.get(amount, 0)
                农商行count = 农商行金额统计.get(amount, 0)
                江苏行count = 江苏行金额统计.get(amount, 0)
                
                if 报表count != 银行count:
                    不匹配明细.append({
                        '金额': amount,
                        '报表数量': 报表count,
                        '银行数量': 银行count,
                        '农商行数量': 农商行count,
                        '江苏行数量': 江苏行count,
                        '差额': 报表count - 银行count
                    })
            
            # 输出不匹配明细
            if 不匹配明细:
                print(f"\n发现 {len(不匹配明细)} 个金额存在数量不匹配:")
                print(f"{'金额':>10} {'报表':>6} {'银行':>6} {'农商行':>6} {'江苏行':>6} {'差额':>6}")
                print("-" * 50)
                
                for item in 不匹配明细[:20]:  # 只显示前20条
                    print(f"{item['金额']:>10.2f} {item['报表数量']:>6} {item['银行数量']:>6} "
                          f"{item['农商行数量']:>6} {item['江苏行数量']:>6} {item['差额']:>6}")
                
                if len(不匹配明细) > 20:
                    print(f"... 还有 {len(不匹配明细) - 20} 条记录")
            
            # 保存详细报告到Excel
            print(f"\n正在生成详细对账报告...")
            with pd.ExcelWriter('对账报告.xlsx', engine='openpyxl') as writer:
                # 汇总页
                汇总数据 = pd.DataFrame({
                    '项目': ['报表总金额', '农商行总金额', '江苏行总金额', '银行合计', '差额'],
                    '金额': [报表总金额, 农商行总金额, 江苏行总金额, 银行总金额, 差额]
                })
                汇总数据.to_excel(writer, sheet_name='汇总', index=False)
                
                # 不匹配明细页
                if 不匹配明细:
                    不匹配df = pd.DataFrame(不匹配明细)
                    不匹配df.to_excel(writer, sheet_name='不匹配明细', index=False)
                
                print("对账报告已保存为 '对账报告.xlsx'")
        else:
            print("\n✅ 账目完全平衡！")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    detailed_reconciliation()
