#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能对账程序 - 处理合并支付、拆分支付、退款冲正等复杂情况
"""

import pandas as pd
import numpy as np
from itertools import combinations

def load_data():
    """加载数据"""
    print("加载数据...")
    
    # 报表数据
    报表数据 = pd.read_excel('7.04报表数.xlsx', sheet_name='Sheet1')
    报表数据['金额'] = pd.to_numeric(报表数据['fkje'], errors='coerce')
    报表数据 = 报表数据.dropna(subset=['金额'])
    
    # 农商行数据
    农商行原始 = pd.read_excel('农商行.xlsx', sheet_name='Sheet1')
    农商行数据 = 农商行原始.iloc[1:].reset_index(drop=True)
    农商行数据.columns = ['标识', '商户号', '营业名称', '交易时间', '终端号', '终端别名', 
                       '交易类型', '支付方式', '订单金额', '商户订单号', '支付流水号']
    农商行数据['金额'] = pd.to_numeric(农商行数据['订单金额'], errors='coerce')
    农商行数据 = 农商行数据.dropna(subset=['金额'])
    农商行数据 = 农商行数据[农商行数据['金额'] > 0]
    
    # 江苏行数据
    江苏行数据 = pd.read_excel('江苏行.xlsx', sheet_name='江苏行')
    江苏行数据['金额'] = pd.to_numeric(江苏行数据['交易金额'], errors='coerce')
    江苏行数据 = 江苏行数据.dropna(subset=['金额'])
    
    return 报表数据, 农商行数据, 江苏行数据

def find_net_amount_matches(报表记录组, 银行金额, 容差=0.01):
    """
    寻找净额匹配：报表中正负金额相加等于银行金额
    """
    if len(报表记录组) < 2:
        return []
    
    匹配组合 = []
    金额列表 = 报表记录组['金额'].tolist()
    
    # 尝试不同数量的组合
    for 组合数 in range(2, min(6, len(金额列表) + 1)):
        for 组合 in combinations(金额列表, 组合数):
            净额 = sum(组合)
            if abs(净额 - 银行金额) <= 容差:
                匹配组合.append(组合)
    
    return 匹配组合

def analyze_unmatched_records(未匹配报表, 未匹配银行):
    """
    深度分析未匹配记录，寻找复杂的匹配关系
    """
    print("\n深度分析未匹配记录...")
    
    复杂匹配结果 = []
    
    # 按病人分组分析报表记录
    报表按病人分组 = 未匹配报表.groupby('brxm')
    
    for 银行idx, 银行记录 in 未匹配银行.iterrows():
        if 银行记录.get('已匹配', False):
            continue
            
        银行金额 = 银行记录['金额']
        找到匹配 = False
        
        # 在每个病人的记录中寻找净额匹配
        for 病人姓名, 病人记录组 in 报表按病人分组:
            if 找到匹配:
                break
                
            病人未匹配记录 = 病人记录组[病人记录组.get('已匹配', False) == False]
            
            if len(病人未匹配记录) == 0:
                continue
            
            # 寻找净额匹配（包括正负金额）
            匹配组合 = find_net_amount_matches(病人未匹配记录, 银行金额)
            
            if 匹配组合:
                选中组合 = 匹配组合[0]
                
                # 分析组合类型
                正数金额 = [x for x in 选中组合 if x > 0]
                负数金额 = [x for x in 选中组合 if x < 0]
                
                匹配类型 = "复杂匹配"
                if len(正数金额) > 0 and len(负数金额) > 0:
                    匹配类型 = "支付退款净额"
                elif len(正数金额) > 1:
                    匹配类型 = "多笔合并支付"
                elif len(负数金额) > 1:
                    匹配类型 = "多笔退款合并"
                
                复杂匹配结果.append({
                    '匹配类型': 匹配类型,
                    '银行金额': 银行金额,
                    '报表净额': sum(选中组合),
                    '银行来源': 银行记录.get('银行来源', ''),
                    '病人姓名': 病人姓名,
                    '银行订单号': 银行记录.get('商户订单号', ''),
                    '报表组合': f"{'+'.join([str(x) for x in 选中组合])}",
                    '正数金额': 正数金额,
                    '负数金额': 负数金额,
                    '组合数量': len(选中组合)
                })
                
                # 标记为已匹配
                组合记录索引 = []
                剩余金额 = list(选中组合)
                
                for idx, 记录 in 病人未匹配记录.iterrows():
                    if 记录['金额'] in 剩余金额:
                        组合记录索引.append(idx)
                        剩余金额.remove(记录['金额'])
                        if len(剩余金额) == 0:
                            break
                
                # 标记相关记录
                for 记录idx in 组合记录索引:
                    未匹配报表.loc[记录idx, '已匹配'] = True
                未匹配银行.loc[银行idx, '已匹配'] = True
                
                找到匹配 = True
    
    return 复杂匹配结果

def intelligent_reconciliation():
    """执行智能对账"""
    try:
        print("=== 智能对账程序 ===")
        
        # 加载数据
        报表数据, 农商行数据, 江苏行数据 = load_data()
        
        print(f"报表记录: {len(报表数据)}条")
        print(f"农商行记录: {len(农商行数据)}条") 
        print(f"江苏行记录: {len(江苏行数据)}条")
        
        # 合并银行数据
        银行数据 = pd.concat([农商行数据, 江苏行数据], ignore_index=True)
        银行数据['银行来源'] = ['农商行'] * len(农商行数据) + ['江苏行'] * len(江苏行数据)
        
        # 添加匹配标记
        报表数据['已匹配'] = False
        银行数据['已匹配'] = False
        
        # 第一阶段：直接匹配
        print("\n第一阶段：直接金额匹配...")
        直接匹配结果 = []
        
        for idx, 银行记录 in 银行数据.iterrows():
            if 银行记录['已匹配']:
                continue
                
            银行金额 = 银行记录['金额']
            
            候选报表 = 报表数据[
                (报表数据['金额'] == 银行金额) & 
                (报表数据['已匹配'] == False)
            ]
            
            if len(候选报表) > 0:
                报表记录 = 候选报表.iloc[0]
                
                直接匹配结果.append({
                    '匹配类型': '直接匹配',
                    '银行金额': 银行金额,
                    '报表金额': 报表记录['金额'],
                    '银行来源': 银行记录['银行来源'],
                    '病人姓名': 报表记录.get('brxm', ''),
                    '银行订单号': 银行记录.get('商户订单号', '')
                })
                
                报表数据.loc[报表记录.name, '已匹配'] = True
                银行数据.loc[idx, '已匹配'] = True
        
        print(f"直接匹配成功: {len(直接匹配结果)}笔")
        
        # 第二阶段：简单合并支付匹配
        print("\n第二阶段：简单合并支付匹配...")
        合并匹配结果 = []
        
        剩余报表 = 报表数据[报表数据['已匹配'] == False]
        剩余银行 = 银行数据[银行数据['已匹配'] == False]
        
        # 只考虑正数金额的合并
        报表正数 = 剩余报表[剩余报表['金额'] > 0]
        报表按病人分组 = 报表正数.groupby('brxm')
        
        for 银行idx, 银行记录 in 剩余银行.iterrows():
            if 银行记录['已匹配']:
                continue
                
            银行金额 = 银行记录['金额']
            找到匹配 = False
            
            for 病人姓名, 病人记录组 in 报表按病人分组:
                if 找到匹配:
                    break
                    
                病人未匹配记录 = 病人记录组[病人记录组['已匹配'] == False]
                
                if len(病人未匹配记录) < 2:
                    continue
                
                金额列表 = 病人未匹配记录['金额'].tolist()
                
                for 组合数 in range(2, min(6, len(金额列表) + 1)):
                    for 组合 in combinations(金额列表, 组合数):
                        if abs(sum(组合) - 银行金额) < 0.01:
                            合并匹配结果.append({
                                '匹配类型': '合并支付',
                                '银行金额': 银行金额,
                                '报表金额': sum(组合),
                                '银行来源': 银行记录['银行来源'],
                                '病人姓名': 病人姓名,
                                '银行订单号': 银行记录.get('商户订单号', ''),
                                '报表组合': f"合并:{'+'.join([str(x) for x in 组合])}"
                            })
                            
                            # 标记为已匹配
                            组合记录索引 = []
                            剩余金额 = list(组合)
                            
                            for idx, 记录 in 病人未匹配记录.iterrows():
                                if 记录['金额'] in 剩余金额:
                                    组合记录索引.append(idx)
                                    剩余金额.remove(记录['金额'])
                                    if len(剩余金额) == 0:
                                        break
                            
                            for 记录idx in 组合记录索引:
                                报表数据.loc[记录idx, '已匹配'] = True
                            银行数据.loc[银行idx, '已匹配'] = True
                            
                            找到匹配 = True
                            break
                    if 找到匹配:
                        break
        
        print(f"合并支付匹配成功: {len(合并匹配结果)}笔")
        
        # 第三阶段：复杂匹配（净额、退款等）
        print("\n第三阶段：复杂匹配分析...")
        
        最终未匹配报表 = 报表数据[报表数据['已匹配'] == False].copy()
        最终未匹配银行 = 银行数据[银行数据['已匹配'] == False].copy()
        
        复杂匹配结果 = analyze_unmatched_records(最终未匹配报表, 最终未匹配银行)
        
        print(f"复杂匹配成功: {len(复杂匹配结果)}笔")
        
        # 最终统计
        总匹配数 = len(直接匹配结果) + len(合并匹配结果) + len(复杂匹配结果)
        最终未匹配报表 = 报表数据[报表数据['已匹配'] == False]
        最终未匹配银行 = 银行数据[银行数据['已匹配'] == False]
        
        print(f"\n=== 智能对账最终结果 ===")
        print(f"直接匹配: {len(直接匹配结果)}笔")
        print(f"合并支付匹配: {len(合并匹配结果)}笔")
        print(f"复杂匹配: {len(复杂匹配结果)}笔")
        print(f"总匹配数: {总匹配数}笔")
        print(f"未匹配报表记录: {len(最终未匹配报表)}条")
        print(f"未匹配银行记录: {len(最终未匹配银行)}条")
        print(f"匹配率: {(总匹配数/len(报表数据))*100:.1f}%")
        
        # 显示复杂匹配示例
        if 复杂匹配结果:
            print(f"\n复杂匹配示例:")
            for i, 匹配 in enumerate(复杂匹配结果):
                print(f"{i+1}. {匹配['匹配类型']} - 病人:{匹配['病人姓名']}, "
                      f"银行:{匹配['银行金额']:.2f}, 报表组合:{匹配['报表组合']}")
        
        # 分析剩余未匹配记录
        print(f"\n剩余未匹配分析:")
        if len(最终未匹配报表) > 0:
            负数记录 = 最终未匹配报表[最终未匹配报表['金额'] < 0]
            正数记录 = 最终未匹配报表[最终未匹配报表['金额'] > 0]
            print(f"  未匹配报表负数记录: {len(负数记录)}条")
            print(f"  未匹配报表正数记录: {len(正数记录)}条")
        
        # 保存报告
        print(f"\n正在保存智能对账报告...")
        with pd.ExcelWriter('智能对账报告.xlsx', engine='openpyxl') as writer:
            if 直接匹配结果:
                pd.DataFrame(直接匹配结果).to_excel(writer, sheet_name='直接匹配', index=False)
            if 合并匹配结果:
                pd.DataFrame(合并匹配结果).to_excel(writer, sheet_name='合并支付', index=False)
            if 复杂匹配结果:
                pd.DataFrame(复杂匹配结果).to_excel(writer, sheet_name='复杂匹配', index=False)
            if len(最终未匹配报表) > 0:
                最终未匹配报表[['brxm', 'fkje', 'fkmc', 'jcddh']].to_excel(
                    writer, sheet_name='未匹配报表', index=False)
            if len(最终未匹配银行) > 0:
                最终未匹配银行[['金额', '银行来源', '商户订单号']].to_excel(
                    writer, sheet_name='未匹配银行', index=False)
        
        print("智能对账报告已保存为 '智能对账报告.xlsx'")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    intelligent_reconciliation()
