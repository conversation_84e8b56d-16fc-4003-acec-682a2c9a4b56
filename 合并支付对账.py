#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
合并支付对账程序 - 处理多笔交易合并支付的情况
"""

import pandas as pd
import numpy as np
from itertools import combinations

def load_data():
    """加载数据"""
    print("加载数据...")
    
    # 报表数据
    报表数据 = pd.read_excel('7.04报表数.xlsx', sheet_name='Sheet1')
    报表数据['金额'] = pd.to_numeric(报表数据['fkje'], errors='coerce')
    报表数据 = 报表数据.dropna(subset=['金额'])
    
    # 农商行数据
    农商行原始 = pd.read_excel('农商行.xlsx', sheet_name='Sheet1')
    农商行数据 = 农商行原始.iloc[1:].reset_index(drop=True)
    农商行数据.columns = ['标识', '商户号', '营业名称', '交易时间', '终端号', '终端别名', 
                       '交易类型', '支付方式', '订单金额', '商户订单号', '支付流水号']
    农商行数据['金额'] = pd.to_numeric(农商行数据['订单金额'], errors='coerce')
    农商行数据 = 农商行数据.dropna(subset=['金额'])
    农商行数据 = 农商行数据[农商行数据['金额'] > 0]
    
    # 江苏行数据
    江苏行数据 = pd.read_excel('江苏行.xlsx', sheet_name='江苏行')
    江苏行数据['金额'] = pd.to_numeric(江苏行数据['交易金额'], errors='coerce')
    江苏行数据 = 江苏行数据.dropna(subset=['金额'])
    
    return 报表数据, 农商行数据, 江苏行数据

def find_combination_matches(报表金额列表, 银行金额, 最大组合数=5):
    """
    寻找报表中多个金额组合等于银行金额的情况
    """
    匹配组合 = []
    
    # 尝试不同数量的组合（2到最大组合数）
    for 组合数 in range(2, min(最大组合数 + 1, len(报表金额列表) + 1)):
        for 组合 in combinations(报表金额列表, 组合数):
            if abs(sum(组合) - 银行金额) < 0.01:  # 允许0.01的误差
                匹配组合.append(组合)
    
    return 匹配组合

def advanced_reconciliation():
    """执行高级对账，包括合并支付检测"""
    try:
        print("=== 合并支付对账程序 ===")
        
        # 加载数据
        报表数据, 农商行数据, 江苏行数据 = load_data()
        
        print(f"报表记录: {len(报表数据)}条")
        print(f"农商行记录: {len(农商行数据)}条") 
        print(f"江苏行记录: {len(江苏行数据)}条")
        
        # 合并银行数据
        银行数据 = pd.concat([农商行数据, 江苏行数据], ignore_index=True)
        银行数据['银行来源'] = ['农商行'] * len(农商行数据) + ['江苏行'] * len(江苏行数据)
        
        print(f"银行总记录: {len(银行数据)}条")
        
        # 第一阶段：直接匹配
        print("\n第一阶段：直接金额匹配...")
        
        直接匹配结果 = []
        未匹配报表 = 报表数据.copy()
        未匹配银行 = 银行数据.copy()
        
        # 为数据添加匹配标记
        未匹配报表['已匹配'] = False
        未匹配银行['已匹配'] = False
        
        # 直接匹配相同金额
        for idx, 银行记录 in 未匹配银行.iterrows():
            if 银行记录['已匹配']:
                continue
                
            银行金额 = 银行记录['金额']
            
            # 在未匹配的报表中寻找相同金额
            候选报表 = 未匹配报表[
                (未匹配报表['金额'] == 银行金额) & 
                (未匹配报表['已匹配'] == False)
            ]
            
            if len(候选报表) > 0:
                # 匹配第一个找到的记录
                报表记录 = 候选报表.iloc[0]
                
                直接匹配结果.append({
                    '匹配类型': '直接匹配',
                    '银行金额': 银行金额,
                    '报表金额': 报表记录['金额'],
                    '银行来源': 银行记录['银行来源'],
                    '病人姓名': 报表记录.get('brxm', ''),
                    '银行订单号': 银行记录.get('商户订单号', ''),
                    '报表组合': f"单笔:{报表记录['金额']}"
                })
                
                # 标记为已匹配
                未匹配报表.loc[报表记录.name, '已匹配'] = True
                未匹配银行.loc[idx, '已匹配'] = True
        
        print(f"直接匹配成功: {len(直接匹配结果)}笔")
        
        # 第二阶段：合并支付匹配
        print("\n第二阶段：合并支付匹配...")
        
        合并匹配结果 = []
        
        # 获取未匹配的数据
        剩余报表 = 未匹配报表[未匹配报表['已匹配'] == False]
        剩余银行 = 未匹配银行[未匹配银行['已匹配'] == False]
        
        print(f"剩余未匹配报表: {len(剩余报表)}条")
        print(f"剩余未匹配银行: {len(剩余银行)}条")
        
        # 按病人姓名分组，寻找可能的合并支付
        报表按病人分组 = 剩余报表.groupby('brxm')
        
        for 银行idx, 银行记录 in 剩余银行.iterrows():
            if 银行记录['已匹配']:
                continue
                
            银行金额 = 银行记录['金额']
            找到匹配 = False
            
            # 在每个病人的记录中寻找组合匹配
            for 病人姓名, 病人记录组 in 报表按病人分组:
                if 找到匹配:
                    break
                    
                # 获取该病人未匹配的记录
                病人未匹配记录 = 病人记录组[病人记录组['已匹配'] == False]
                
                if len(病人未匹配记录) < 2:  # 至少需要2条记录才能组合
                    continue
                
                # 提取金额列表
                病人金额列表 = 病人未匹配记录['金额'].tolist()
                
                # 寻找组合匹配
                匹配组合 = find_combination_matches(病人金额列表, 银行金额, 最大组合数=5)
                
                if 匹配组合:
                    # 使用第一个找到的组合
                    选中组合 = 匹配组合[0]
                    
                    # 找到对应的记录索引
                    组合记录索引 = []
                    剩余金额 = list(选中组合)
                    
                    for idx, 记录 in 病人未匹配记录.iterrows():
                        if 记录['金额'] in 剩余金额:
                            组合记录索引.append(idx)
                            剩余金额.remove(记录['金额'])
                            if len(剩余金额) == 0:
                                break
                    
                    if len(组合记录索引) == len(选中组合):
                        合并匹配结果.append({
                            '匹配类型': '合并支付',
                            '银行金额': 银行金额,
                            '报表金额': sum(选中组合),
                            '银行来源': 银行记录['银行来源'],
                            '病人姓名': 病人姓名,
                            '银行订单号': 银行记录.get('商户订单号', ''),
                            '报表组合': f"合并:{'+'.join([str(x) for x in 选中组合])}",
                            '组合数量': len(选中组合)
                        })
                        
                        # 标记相关记录为已匹配
                        for 记录idx in 组合记录索引:
                            未匹配报表.loc[记录idx, '已匹配'] = True
                        未匹配银行.loc[银行idx, '已匹配'] = True
                        
                        找到匹配 = True
                        break
        
        print(f"合并支付匹配成功: {len(合并匹配结果)}笔")
        
        # 第三阶段：统计最终结果
        print("\n第三阶段：统计最终结果...")
        
        总匹配数 = len(直接匹配结果) + len(合并匹配结果)
        最终未匹配报表 = 未匹配报表[未匹配报表['已匹配'] == False]
        最终未匹配银行 = 未匹配银行[未匹配银行['已匹配'] == False]
        
        print(f"\n=== 最终对账结果 ===")
        print(f"直接匹配: {len(直接匹配结果)}笔")
        print(f"合并支付匹配: {len(合并匹配结果)}笔")
        print(f"总匹配数: {总匹配数}笔")
        print(f"未匹配报表记录: {len(最终未匹配报表)}条")
        print(f"未匹配银行记录: {len(最终未匹配银行)}条")
        print(f"匹配率: {(总匹配数/len(报表数据))*100:.1f}%")
        
        # 显示合并支付的例子
        if 合并匹配结果:
            print(f"\n合并支付示例 (前5个):")
            for i, 匹配 in enumerate(合并匹配结果[:5]):
                print(f"{i+1}. 病人:{匹配['病人姓名']}, 银行金额:{匹配['银行金额']:.2f}, "
                      f"报表组合:{匹配['报表组合']}, 来源:{匹配['银行来源']}")
        
        # 显示未匹配的记录
        if len(最终未匹配报表) > 0:
            print(f"\n未匹配报表记录 (前10条):")
            for idx, 记录 in 最终未匹配报表.head(10).iterrows():
                print(f"  金额:{记录['金额']:>8.2f}, 病人:{记录.get('brxm', '')}, "
                      f"付款名称:{记录.get('fkmc', '')}")
        
        if len(最终未匹配银行) > 0:
            print(f"\n未匹配银行记录 (前10条):")
            for idx, 记录 in 最终未匹配银行.head(10).iterrows():
                print(f"  金额:{记录['金额']:>8.2f}, 来源:{记录['银行来源']}, "
                      f"订单号:{记录.get('商户订单号', '')}")
        
        # 保存详细报告
        print(f"\n正在保存合并支付对账报告...")
        with pd.ExcelWriter('合并支付对账报告.xlsx', engine='openpyxl') as writer:
            # 直接匹配结果
            if 直接匹配结果:
                pd.DataFrame(直接匹配结果).to_excel(writer, sheet_name='直接匹配', index=False)
            
            # 合并支付匹配结果
            if 合并匹配结果:
                pd.DataFrame(合并匹配结果).to_excel(writer, sheet_name='合并支付匹配', index=False)
            
            # 未匹配记录
            if len(最终未匹配报表) > 0:
                最终未匹配报表[['brxm', 'fkje', 'fkmc', 'jcddh', 'yhddh']].to_excel(
                    writer, sheet_name='未匹配报表', index=False)
            
            if len(最终未匹配银行) > 0:
                最终未匹配银行[['金额', '银行来源', '商户订单号', '交易时间']].to_excel(
                    writer, sheet_name='未匹配银行', index=False)
        
        print("合并支付对账报告已保存为 '合并支付对账报告.xlsx'")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    advanced_reconciliation()
