#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终对账程序 - 修复所有数据解析问题
"""

import pandas as pd
import numpy as np

def parse_nongshang_data_correctly():
    """正确解析农商行数据"""
    print("正确解析农商行数据...")
    
    # 读取原始数据
    农商行原始 = pd.read_excel('农商行.xlsx', sheet_name='Sheet1')
    
    # 从第2行开始（跳过标题行）
    农商行数据 = 农商行原始.iloc[1:].reset_index(drop=True)
    
    # 设置正确的列名
    农商行数据.columns = ['标识', '商户号', '营业名称', '交易时间', '终端号', '终端别名', 
                       '交易类型', '支付方式', '订单金额', '商户订单号', '支付流水号']
    
    # 正确提取金额列（第9列，索引为8）
    农商行数据['金额'] = pd.to_numeric(农商行数据['订单金额'], errors='coerce')
    
    # 清理数据
    农商行数据 = 农商行数据.dropna(subset=['金额'])
    农商行数据 = 农商行数据[农商行数据['金额'] > 0]  # 只保留正数金额
    
    print(f"农商行解析结果: {len(农商行数据)}条有效记录")
    if len(农商行数据) > 0:
        print(f"金额范围: {农商行数据['金额'].min():.2f} - {农商行数据['金额'].max():.2f}")
        print(f"前5条金额: {农商行数据['金额'].head().tolist()}")
    
    return 农商行数据

def final_reconciliation():
    """执行最终对账"""
    try:
        print("=== 最终对账程序 ===\n")
        
        # 1. 读取报表数据
        print("1. 读取报表数据...")
        报表数据 = pd.read_excel('7.04报表数.xlsx', sheet_name='Sheet1')
        报表数据['金额'] = pd.to_numeric(报表数据['fkje'], errors='coerce')
        报表数据 = 报表数据.dropna(subset=['金额'])
        
        # 分离正负金额
        报表正数 = 报表数据[报表数据['金额'] > 0]
        报表负数 = 报表数据[报表数据['金额'] < 0]
        报表总金额 = 报表数据['金额'].sum()
        
        print(f"   报表总记录: {len(报表数据)}条")
        print(f"   正数记录: {len(报表正数)}条, 金额: {报表正数['金额'].sum():.2f}")
        print(f"   负数记录: {len(报表负数)}条, 金额: {报表负数['金额'].sum():.2f}")
        print(f"   报表净金额: {报表总金额:.2f}")
        
        # 2. 解析农商行数据
        print("\n2. 解析农商行数据...")
        农商行数据 = parse_nongshang_data_correctly()
        农商行总金额 = 农商行数据['金额'].sum() if len(农商行数据) > 0 else 0
        print(f"   农商行总金额: {农商行总金额:.2f}")
        
        # 3. 读取江苏行数据
        print("\n3. 读取江苏行数据...")
        江苏行数据 = pd.read_excel('江苏行.xlsx', sheet_name='江苏行')
        江苏行数据['金额'] = pd.to_numeric(江苏行数据['交易金额'], errors='coerce')
        江苏行数据 = 江苏行数据.dropna(subset=['金额'])
        
        # 分离正负金额
        江苏行正数 = 江苏行数据[江苏行数据['金额'] > 0]
        江苏行负数 = 江苏行数据[江苏行数据['金额'] < 0]
        江苏行总金额 = 江苏行数据['金额'].sum()
        
        print(f"   江苏行总记录: {len(江苏行数据)}条")
        print(f"   正数记录: {len(江苏行正数)}条, 金额: {江苏行正数['金额'].sum():.2f}")
        print(f"   负数记录: {len(江苏行负数)}条, 金额: {江苏行负数['金额'].sum():.2f}")
        print(f"   江苏行净金额: {江苏行总金额:.2f}")
        
        # 4. 对账汇总
        银行总金额 = 农商行总金额 + 江苏行总金额
        差额 = 报表总金额 - 银行总金额
        
        print(f"\n=== 对账汇总结果 ===")
        print(f"报表净金额:      {报表总金额:>15,.2f}")
        print(f"农商行总金额:    {农商行总金额:>15,.2f}")
        print(f"江苏行净金额:    {江苏行总金额:>15,.2f}")
        print(f"银行合计金额:    {银行总金额:>15,.2f}")
        print(f"差额:            {差额:>15,.2f}")
        
        # 5. 详细分析不匹配情况
        if abs(差额) > 0.01:
            print(f"\n=== 不匹配分析 ===")
            
            # 按金额分组统计
            def get_amount_counts(df, name):
                if len(df) > 0:
                    counts = df['金额'].value_counts().sort_index()
                    return counts
                return pd.Series(dtype=int)
            
            报表金额统计 = get_amount_counts(报表数据, "报表")
            农商行金额统计 = get_amount_counts(农商行数据, "农商行")
            江苏行金额统计 = get_amount_counts(江苏行数据, "江苏行")
            
            # 合并银行统计
            银行金额统计 = 农商行金额统计.add(江苏行金额统计, fill_value=0)
            
            # 找出不匹配的金额
            all_amounts = set(报表金额统计.index) | set(银行金额统计.index)
            
            不匹配明细 = []
            匹配明细 = []
            
            for amount in sorted(all_amounts):
                报表count = 报表金额统计.get(amount, 0)
                银行count = int(银行金额统计.get(amount, 0))
                农商行count = 农商行金额统计.get(amount, 0)
                江苏行count = 江苏行金额统计.get(amount, 0)
                
                if 报表count != 银行count:
                    不匹配明细.append({
                        '金额': amount,
                        '报表数量': 报表count,
                        '银行数量': 银行count,
                        '农商行数量': 农商行count,
                        '江苏行数量': 江苏行count,
                        '差额数量': 报表count - 银行count,
                        '差额金额': (报表count - 银行count) * amount
                    })
                else:
                    匹配明细.append({
                        '金额': amount,
                        '数量': 报表count
                    })
            
            # 输出统计信息
            print(f"匹配的金额种类: {len(匹配明细)}个")
            print(f"不匹配的金额种类: {len(不匹配明细)}个")
            
            if 不匹配明细:
                print(f"\n不匹配明细 (前20条):")
                print(f"{'金额':>10} {'报表':>6} {'银行':>6} {'农商行':>6} {'江苏行':>6} {'差额数量':>8} {'差额金额':>12}")
                print("-" * 70)
                
                for item in sorted(不匹配明细, key=lambda x: abs(x['差额金额']), reverse=True)[:20]:
                    print(f"{item['金额']:>10.2f} {item['报表数量']:>6} {item['银行数量']:>6} "
                          f"{item['农商行数量']:>6} {item['江苏行数量']:>6} {item['差额数量']:>8} {item['差额金额']:>12.2f}")
            
            # 保存详细报告
            print(f"\n正在生成详细对账报告...")
            with pd.ExcelWriter('最终对账报告.xlsx', engine='openpyxl') as writer:
                # 汇总页
                汇总数据 = pd.DataFrame({
                    '项目': ['报表净金额', '农商行总金额', '江苏行净金额', '银行合计', '差额'],
                    '金额': [报表总金额, 农商行总金额, 江苏行总金额, 银行总金额, 差额]
                })
                汇总数据.to_excel(writer, sheet_name='汇总', index=False)
                
                # 不匹配明细页
                if 不匹配明细:
                    不匹配df = pd.DataFrame(不匹配明细)
                    不匹配df.to_excel(writer, sheet_name='不匹配明细', index=False)
                
                # 匹配明细页
                if 匹配明细:
                    匹配df = pd.DataFrame(匹配明细)
                    匹配df.to_excel(writer, sheet_name='匹配明细', index=False)
                
                print("最终对账报告已保存为 '最终对账报告.xlsx'")
        else:
            print("\n✅ 账目完全平衡！")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    final_reconciliation()
