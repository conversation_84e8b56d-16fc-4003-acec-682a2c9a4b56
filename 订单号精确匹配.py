#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
订单号精确匹配程序 - 通过订单号精确匹配银行数据和报表数据
"""

import pandas as pd
import numpy as np

def load_data():
    """加载数据"""
    print("加载数据...")
    
    # 报表数据
    报表数据 = pd.read_excel('7.04报表数.xlsx', sheet_name='Sheet1')
    报表数据['金额'] = pd.to_numeric(报表数据['fkje'], errors='coerce')
    报表数据 = 报表数据.dropna(subset=['金额'])
    
    # 农商行数据
    农商行原始 = pd.read_excel('农商行.xlsx', sheet_name='Sheet1')
    农商行数据 = 农商行原始.iloc[1:].reset_index(drop=True)
    农商行数据.columns = ['标识', '商户号', '营业名称', '交易时间', '终端号', '终端别名', 
                       '交易类型', '支付方式', '订单金额', '商户订单号', '支付流水号']
    农商行数据['金额'] = pd.to_numeric(农商行数据['订单金额'], errors='coerce')
    农商行数据 = 农商行数据.dropna(subset=['金额'])
    农商行数据 = 农商行数据[农商行数据['金额'] > 0]
    
    # 江苏行数据
    江苏行数据 = pd.read_excel('江苏行.xlsx', sheet_name='江苏行')
    江苏行数据['金额'] = pd.to_numeric(江苏行数据['交易金额'], errors='coerce')
    江苏行数据 = 江苏行数据.dropna(subset=['金额'])
    
    return 报表数据, 农商行数据, 江苏行数据

def analyze_specific_order():
    """分析特定订单号"""
    
    报表数据, 农商行数据, 江苏行数据 = load_data()
    
    # 查找特定订单号
    目标订单号 = 'S202507040921160220970593'
    
    print(f"=== 分析订单号: {目标订单号} ===")
    
    # 在农商行数据中查找
    农商行匹配 = 农商行数据[农商行数据['商户订单号'].str.contains(目标订单号, na=False)]
    
    if len(农商行匹配) > 0:
        print(f"\n在农商行数据中找到匹配:")
        for idx, 记录 in 农商行匹配.iterrows():
            print(f"  金额: {记录['金额']}")
            print(f"  订单号: {记录['商户订单号']}")
            print(f"  交易时间: {记录['交易时间']}")
            print(f"  支付方式: {记录['支付方式']}")
    
    # 在江苏行数据中查找
    江苏行匹配 = 江苏行数据[江苏行数据['商户订单号'].str.contains(目标订单号, na=False)]
    
    if len(江苏行匹配) > 0:
        print(f"\n在江苏行数据中找到匹配:")
        for idx, 记录 in 江苏行匹配.iterrows():
            print(f"  金额: {记录['金额']}")
            print(f"  订单号: {记录['商户订单号']}")
            print(f"  交易时间: {记录.get('交易时间', '')}")
    
    # 在报表数据中查找相关记录
    if len(农商行匹配) > 0:
        银行金额 = 农商行匹配.iloc[0]['金额']
        print(f"\n在报表数据中查找金额 {银行金额} 的记录:")
        
        # 查找相同金额
        相同金额记录 = 报表数据[报表数据['金额'] == 银行金额]
        if len(相同金额记录) > 0:
            print(f"  找到相同金额记录 {len(相同金额记录)} 条:")
            for idx, 记录 in 相同金额记录.iterrows():
                print(f"    病人: {记录.get('brxm', '')}, 金额: {记录['金额']}, 付款名称: {记录.get('fkmc', '')}")
        
        # 查找可能的组合
        print(f"\n查找可能的组合匹配:")
        找到组合 = False
        
        for 病人姓名, 病人记录 in 报表数据.groupby('brxm'):
            金额列表 = 病人记录['金额'].tolist()
            
            # 检查是否有组合等于银行金额
            from itertools import combinations
            for 组合数 in range(2, min(6, len(金额列表) + 1)):
                for 组合 in combinations(金额列表, 组合数):
                    if abs(sum(组合) - 银行金额) < 0.01:
                        print(f"    病人: {病人姓名}")
                        print(f"    组合: {' + '.join([str(x) for x in 组合])} = {sum(组合)}")
                        
                        # 显示具体记录
                        for 金额 in 组合:
                            匹配记录 = 病人记录[病人记录['金额'] == 金额]
                            if len(匹配记录) > 0:
                                记录 = 匹配记录.iloc[0]
                                print(f"      {金额}: {记录.get('fkmc', '')}")
                        
                        找到组合 = True
                        break
                if 找到组合:
                    break
            if 找到组合:
                break

def analyze_all_order_mappings():
    """分析所有订单号的映射关系"""
    
    报表数据, 农商行数据, 江苏行数据 = load_data()
    
    print(f"\n=== 分析所有订单号映射关系 ===")
    
    # 检查报表数据中是否有订单号字段
    print(f"报表数据列名: {list(报表数据.columns)}")
    
    # 查看报表数据中可能包含订单号的字段
    if 'jcddh' in 报表数据.columns:
        print(f"\n报表数据中的jcddh字段示例:")
        print(报表数据['jcddh'].head(10).tolist())
    
    if 'yhddh' in 报表数据.columns:
        print(f"\n报表数据中的yhddh字段示例:")
        print(报表数据['yhddh'].head(10).tolist())
    
    # 分析几个具体的银行订单号
    测试订单号列表 = [
        'S202507040921160220970593',  # 39.96元
        'S202507040807400220930199',  # 52.86元
        'S202507041054220220012351',  # 71.54元
    ]
    
    for 订单号 in 测试订单号列表:
        print(f"\n--- 分析订单号: {订单号} ---")
        
        # 在农商行数据中查找
        农商行匹配 = 农商行数据[农商行数据['商户订单号'].str.contains(订单号, na=False)]
        
        if len(农商行匹配) > 0:
            银行记录 = 农商行匹配.iloc[0]
            银行金额 = 银行记录['金额']
            print(f"银行金额: {银行金额}")
            
            # 在报表数据中查找包含此订单号的记录
            if 'jcddh' in 报表数据.columns:
                报表匹配_jcddh = 报表数据[报表数据['jcddh'].str.contains(订单号, na=False)]
                if len(报表匹配_jcddh) > 0:
                    print(f"在报表jcddh字段中找到匹配:")
                    for idx, 记录 in 报表匹配_jcddh.iterrows():
                        print(f"  病人: {记录.get('brxm', '')}, 金额: {记录['金额']}")
            
            if 'yhddh' in 报表数据.columns:
                报表匹配_yhddh = 报表数据[报表数据['yhddh'].str.contains(订单号, na=False)]
                if len(报表匹配_yhddh) > 0:
                    print(f"在报表yhddh字段中找到匹配:")
                    for idx, 记录 in 报表匹配_yhddh.iterrows():
                        print(f"  病人: {记录.get('brxm', '')}, 金额: {记录['金额']}")
            
            # 如果没有直接的订单号匹配，查找金额匹配
            if 'jcddh' in 报表数据.columns:
                报表匹配_jcddh = 报表数据[报表数据['jcddh'].str.contains(订单号, na=False)]
            else:
                报表匹配_jcddh = pd.DataFrame()
                
            if 'yhddh' in 报表数据.columns:
                报表匹配_yhddh = 报表数据[报表数据['yhddh'].str.contains(订单号, na=False)]
            else:
                报表匹配_yhddh = pd.DataFrame()
            
            if len(报表匹配_jcddh) == 0 and len(报表匹配_yhddh) == 0:
                print(f"未找到订单号直接匹配，查找金额匹配:")
                相同金额记录 = 报表数据[报表数据['金额'] == 银行金额]
                if len(相同金额记录) > 0:
                    print(f"  找到相同金额记录:")
                    for idx, 记录 in 相同金额记录.iterrows():
                        print(f"    病人: {记录.get('brxm', '')}, 金额: {记录['金额']}")

def main():
    try:
        # 分析特定订单号
        analyze_specific_order()
        
        # 分析所有订单号映射关系
        analyze_all_order_mappings()
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
