#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
银行单边数据检测程序 - 找出银行中有但报表中完全没有的真正单边数据
"""

import pandas as pd
import numpy as np
from itertools import combinations

def load_data():
    """加载数据"""
    print("加载数据...")
    
    # 报表数据
    报表数据 = pd.read_excel('7.04报表数.xlsx', sheet_name='Sheet1')
    报表数据['金额'] = pd.to_numeric(报表数据['fkje'], errors='coerce')
    报表数据 = 报表数据.dropna(subset=['金额'])
    
    # 农商行数据
    农商行原始 = pd.read_excel('农商行.xlsx', sheet_name='Sheet1')
    农商行数据 = 农商行原始.iloc[1:].reset_index(drop=True)
    农商行数据.columns = ['标识', '商户号', '营业名称', '交易时间', '终端号', '终端别名', 
                       '交易类型', '支付方式', '订单金额', '商户订单号', '支付流水号']
    农商行数据['金额'] = pd.to_numeric(农商行数据['订单金额'], errors='coerce')
    农商行数据 = 农商行数据.dropna(subset=['金额'])
    农商行数据 = 农商行数据[农商行数据['金额'] > 0]
    农商行数据['银行来源'] = '农商行'
    
    # 江苏行数据
    江苏行数据 = pd.read_excel('江苏行.xlsx', sheet_name='江苏行')
    江苏行数据['金额'] = pd.to_numeric(江苏行数据['交易金额'], errors='coerce')
    江苏行数据 = 江苏行数据.dropna(subset=['金额'])
    江苏行数据['银行来源'] = '江苏行'
    
    return 报表数据, 农商行数据, 江苏行数据

def check_combination_match(银行金额, 报表数据, 最大组合数=5):
    """
    检查银行金额是否能在报表中找到任何组合匹配
    """
    # 1. 直接匹配
    if 银行金额 in 报表数据['金额'].values:
        return True, "直接匹配"
    
    # 2. 按病人分组寻找组合匹配
    for 病人姓名, 病人记录 in 报表数据.groupby('brxm'):
        金额列表 = 病人记录['金额'].tolist()
        
        # 尝试不同数量的组合
        for 组合数 in range(2, min(最大组合数 + 1, len(金额列表) + 1)):
            for 组合 in combinations(金额列表, 组合数):
                if abs(sum(组合) - 银行金额) < 0.01:
                    return True, f"组合匹配-病人:{病人姓名}"
    
    # 3. 寻找净额匹配（包含正负金额）
    for 病人姓名, 病人记录 in 报表数据.groupby('brxm'):
        金额列表 = 病人记录['金额'].tolist()
        
        for 组合数 in range(2, min(最大组合数 + 1, len(金额列表) + 1)):
            for 组合 in combinations(金额列表, 组合数):
                净额 = sum(组合)
                if abs(净额 - 银行金额) < 0.01:
                    负数 = [x for x in 组合 if x < 0]
                    if len(负数) > 0:  # 包含退款的净额匹配
                        return True, f"净额匹配-病人:{病人姓名}"
    
    return False, "无匹配"

def detect_bank_only_records():
    """检测银行单边数据"""
    try:
        print("=== 银行单边数据检测 ===")
        
        # 加载数据
        报表数据, 农商行数据, 江苏行数据 = load_data()
        
        print(f"报表记录: {len(报表数据)}条")
        print(f"农商行记录: {len(农商行数据)}条") 
        print(f"江苏行记录: {len(江苏行数据)}条")
        
        # 合并银行数据
        银行数据 = pd.concat([农商行数据, 江苏行数据], ignore_index=True)
        
        print(f"银行总记录: {len(银行数据)}条")
        
        # 检测每条银行记录
        print("\n开始检测银行单边数据...")
        
        真正单边记录 = []
        有匹配记录 = []
        
        for idx, 银行记录 in 银行数据.iterrows():
            银行金额 = 银行记录['金额']
            银行来源 = 银行记录['银行来源']
            订单号 = 银行记录.get('商户订单号', '')
            
            # 检查是否能找到匹配
            有匹配, 匹配类型 = check_combination_match(银行金额, 报表数据)
            
            if 有匹配:
                有匹配记录.append({
                    '金额': 银行金额,
                    '银行来源': 银行来源,
                    '订单号': 订单号,
                    '匹配类型': 匹配类型
                })
            else:
                真正单边记录.append({
                    '金额': 银行金额,
                    '银行来源': 银行来源,
                    '订单号': 订单号,
                    '交易时间': 银行记录.get('交易时间', ''),
                    '支付方式': 银行记录.get('支付方式', 银行记录.get('交易方式', '')),
                    '状态': '银行单边数据'
                })
        
        # 输出结果
        print(f"\n=== 检测结果 ===")
        print(f"银行总记录: {len(银行数据)}条")
        print(f"找到匹配的记录: {len(有匹配记录)}条")
        print(f"真正的银行单边记录: {len(真正单边记录)}条")
        print(f"匹配率: {(len(有匹配记录)/len(银行数据))*100:.1f}%")
        
        if 真正单边记录:
            print(f"\n真正的银行单边数据明细:")
            print(f"{'金额':>10} {'银行来源':>8} {'订单号':>25} {'支付方式':>10}")
            print("-" * 60)
            
            # 按金额排序显示
            真正单边记录_sorted = sorted(真正单边记录, key=lambda x: x['金额'], reverse=True)
            
            for 记录 in 真正单边记录_sorted:
                print(f"{记录['金额']:>10.2f} {记录['银行来源']:>8} {记录['订单号']:>25} {记录['支付方式']:>10}")
        
        # 分析单边记录的特征
        if 真正单边记录:
            print(f"\n=== 单边记录特征分析 ===")
            
            # 按银行来源分组
            农商行单边 = [r for r in 真正单边记录 if r['银行来源'] == '农商行']
            江苏行单边 = [r for r in 真正单边记录 if r['银行来源'] == '江苏行']
            
            print(f"农商行单边记录: {len(农商行单边)}条")
            print(f"江苏行单边记录: {len(江苏行单边)}条")
            
            # 金额分布分析
            单边金额列表 = [r['金额'] for r in 真正单边记录]
            print(f"单边记录金额范围: {min(单边金额列表):.2f} - {max(单边金额列表):.2f}")
            print(f"单边记录总金额: {sum(单边金额列表):.2f}")
            
            # 小额交易分析
            小额记录 = [r for r in 真正单边记录 if r['金额'] <= 10]
            大额记录 = [r for r in 真正单边记录 if r['金额'] > 100]
            
            print(f"小额交易(≤10元): {len(小额记录)}条")
            print(f"大额交易(>100元): {len(大额记录)}条")
            
            if 小额记录:
                print(f"小额交易明细:")
                for 记录 in 小额记录[:10]:  # 显示前10条
                    print(f"  {记录['金额']:>6.2f}元 - {记录['银行来源']} - {记录['订单号']}")
        
        # 保存详细报告
        print(f"\n正在保存银行单边数据检测报告...")
        with pd.ExcelWriter('银行单边数据检测报告.xlsx', engine='openpyxl') as writer:
            if 真正单边记录:
                pd.DataFrame(真正单边记录).to_excel(writer, sheet_name='银行单边数据', index=False)
            
            if 有匹配记录:
                # 按匹配类型分组保存
                匹配df = pd.DataFrame(有匹配记录)
                匹配df.to_excel(writer, sheet_name='有匹配的银行记录', index=False)
                
                # 统计各种匹配类型
                匹配类型统计 = 匹配df['匹配类型'].value_counts()
                匹配类型统计.to_excel(writer, sheet_name='匹配类型统计')
        
        print("银行单边数据检测报告已保存为 '银行单边数据检测报告.xlsx'")
        
        # 最终总结
        print(f"\n=== 最终总结 ===")
        if len(真正单边记录) == 0:
            print("🎉 恭喜！没有发现真正的银行单边数据，所有银行记录都能在报表中找到对应关系。")
        else:
            print(f"⚠️  发现 {len(真正单边记录)} 条真正的银行单边数据，需要进一步核查：")
            print("   - 可能是测试交易")
            print("   - 可能是手续费")
            print("   - 可能是系统间数据同步问题")
            print("   - 可能是时间差导致的数据不一致")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    detect_bank_only_records()
