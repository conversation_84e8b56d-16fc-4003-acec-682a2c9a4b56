#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
银行与报表数据对账程序
"""

import pandas as pd
import numpy as np
from datetime import datetime

def load_data():
    """
    加载三个Excel文件的数据
    
    Returns:
        tuple: (报表数据, 农商行数据, 江苏行数据)
    """
    # 读取报表数据
    报表数据 = pd.read_excel('7.04报表数.xlsx', sheet_name='Sheet1')
    
    # 读取农商行数据
    农商行数据 = pd.read_excel('农商行.xlsx', sheet_name='Sheet1')
    
    # 读取江苏行数据
    江苏行数据 = pd.read_excel('江苏行.xlsx', sheet_name='江苏行')
    
    return 报表数据, 农商行数据, 江苏行数据

def clean_and_prepare_data(报表数据, 农商行数据, 江苏行数据):
    """
    清理和准备数据用于对账
    
    Args:
        报表数据: 报表数据DataFrame
        农商行数据: 农商行数据DataFrame
        江苏行数据: 江苏行数据DataFrame
    
    Returns:
        tuple: 清理后的数据
    """
    print("正在清理和准备数据...")
    
    # 清理报表数据
    报表数据_clean = 报表数据.copy()
    if 'fkje' in 报表数据_clean.columns:
        报表数据_clean['金额'] = pd.to_numeric(报表数据_clean['fkje'], errors='coerce')
    
    # 清理农商行数据
    农商行数据_clean = 农商行数据.copy()
    # 跳过前几行标题行，找到实际数据
    for i, row in 农商行数据_clean.iterrows():
        if '商户订单号' in str(row.values):
            农商行数据_clean = 农商行数据_clean.iloc[i+1:].reset_index(drop=True)
            break
    
    # 设置正确的列名
    if len(农商行数据_clean.columns) >= 11:
        农商行数据_clean.columns = ['商户号', '营业名称', '交易时间', '终端号', '终端别名', 
                                   '交易类型', '支付方式', '订单金额', '商户订单号', '支付流水号', '其他']
        农商行数据_clean['金额'] = pd.to_numeric(农商行数据_clean['订单金额'], errors='coerce')
    
    # 清理江苏行数据
    江苏行数据_clean = 江苏行数据.copy()
    if '交易金额' in 江苏行数据_clean.columns:
        江苏行数据_clean['金额'] = pd.to_numeric(江苏行数据_clean['交易金额'], errors='coerce')
    
    return 报表数据_clean, 农商行数据_clean, 江苏行数据_clean

def perform_reconciliation(报表数据, 农商行数据, 江苏行数据):
    """
    执行对账操作
    
    Args:
        报表数据: 清理后的报表数据
        农商行数据: 清理后的农商行数据
        江苏行数据: 清理后的江苏行数据
    
    Returns:
        dict: 对账结果
    """
    print("开始执行对账...")
    
    # 计算总金额
    报表总金额 = 报表数据['金额'].sum() if '金额' in 报表数据.columns else 0
    农商行总金额 = 农商行数据['金额'].sum() if '金额' in 农商行数据.columns else 0
    江苏行总金额 = 江苏行数据['金额'].sum() if '金额' in 江苏行数据.columns else 0
    
    银行总金额 = 农商行总金额 + 江苏行总金额
    
    print(f"\n=== 金额汇总 ===")
    print(f"报表总金额: {报表总金额:,.2f}")
    print(f"农商行总金额: {农商行总金额:,.2f}")
    print(f"江苏行总金额: {江苏行总金额:,.2f}")
    print(f"银行合计金额: {银行总金额:,.2f}")
    print(f"差额: {报表总金额 - 银行总金额:,.2f}")
    
    # 准备对账结果
    对账结果 = {
        '报表总金额': 报表总金额,
        '农商行总金额': 农商行总金额,
        '江苏行总金额': 江苏行总金额,
        '银行总金额': 银行总金额,
        '差额': 报表总金额 - 银行总金额,
        '报表数据': 报表数据,
        '农商行数据': 农商行数据,
        '江苏行数据': 江苏行数据
    }
    
    return 对账结果

def find_unmatched_records(对账结果):
    """
    查找不匹配的记录
    
    Args:
        对账结果: 对账结果字典
    
    Returns:
        dict: 不匹配的记录
    """
    print("\n正在查找不匹配的记录...")
    
    报表数据 = 对账结果['报表数据']
    农商行数据 = 对账结果['农商行数据']
    江苏行数据 = 对账结果['江苏行数据']
    
    # 创建金额分组
    def group_by_amount(df, amount_col):
        if amount_col in df.columns:
            return df.groupby(amount_col).size().reset_index(name='count')
        return pd.DataFrame()
    
    报表金额统计 = group_by_amount(报表数据, '金额')
    农商行金额统计 = group_by_amount(农商行数据, '金额')
    江苏行金额统计 = group_by_amount(江苏行数据, '金额')
    
    # 合并银行数据统计
    银行金额统计 = pd.concat([农商行金额统计, 江苏行金额统计]).groupby('金额')['count'].sum().reset_index()
    
    # 找出不匹配的金额
    不匹配记录 = {
        '仅在报表中': [],
        '仅在银行中': [],
        '数量不匹配': []
    }
    
    # 检查每个金额
    all_amounts = set()
    if not 报表金额统计.empty:
        all_amounts.update(报表金额统计['金额'].dropna())
    if not 银行金额统计.empty:
        all_amounts.update(银行金额统计['金额'].dropna())
    
    for amount in all_amounts:
        if pd.isna(amount):
            continue
            
        报表count = 报表金额统计[报表金额统计['金额'] == amount]['count'].sum() if not 报表金额统计.empty else 0
        银行count = 银行金额统计[银行金额统计['金额'] == amount]['count'].sum() if not 银行金额统计.empty else 0
        
        if 报表count > 0 and 银行count == 0:
            不匹配记录['仅在报表中'].append({'金额': amount, '报表数量': 报表count})
        elif 报表count == 0 and 银行count > 0:
            不匹配记录['仅在银行中'].append({'金额': amount, '银行数量': 银行count})
        elif 报表count != 银行count:
            不匹配记录['数量不匹配'].append({
                '金额': amount, 
                '报表数量': 报表count, 
                '银行数量': 银行count,
                '差额': 报表count - 银行count
            })
    
    return 不匹配记录

def main():
    """
    主函数
    """
    try:
        print("开始对账程序...")
        
        # 加载数据
        报表数据, 农商行数据, 江苏行数据 = load_data()
        
        # 清理数据
        报表数据_clean, 农商行数据_clean, 江苏行数据_clean = clean_and_prepare_data(
            报表数据, 农商行数据, 江苏行数据
        )
        
        # 执行对账
        对账结果 = perform_reconciliation(报表数据_clean, 农商行数据_clean, 江苏行数据_clean)
        
        # 查找不匹配记录
        不匹配记录 = find_unmatched_records(对账结果)
        
        # 输出不匹配明细
        print(f"\n=== 不匹配记录明细 ===")
        
        if 不匹配记录['仅在报表中']:
            print(f"\n仅在报表中存在的记录 ({len(不匹配记录['仅在报表中'])}条):")
            for record in 不匹配记录['仅在报表中']:
                print(f"  金额: {record['金额']}, 数量: {record['报表数量']}")
        
        if 不匹配记录['仅在银行中']:
            print(f"\n仅在银行中存在的记录 ({len(不匹配记录['仅在银行中'])}条):")
            for record in 不匹配记录['仅在银行中']:
                print(f"  金额: {record['金额']}, 数量: {record['银行数量']}")
        
        if 不匹配记录['数量不匹配']:
            print(f"\n数量不匹配的记录 ({len(不匹配记录['数量不匹配'])}条):")
            for record in 不匹配记录['数量不匹配']:
                print(f"  金额: {record['金额']}, 报表数量: {record['报表数量']}, "
                      f"银行数量: {record['银行数量']}, 差额: {record['差额']}")
        
        if (not 不匹配记录['仅在报表中'] and 
            not 不匹配记录['仅在银行中'] and 
            not 不匹配记录['数量不匹配']):
            print("\n✅ 恭喜！所有记录都已匹配，账目平衡。")
        
        return 对账结果, 不匹配记录
        
    except Exception as e:
        print(f"对账过程中出现错误: {e}")
        return None, None

if __name__ == "__main__":
    对账结果, 不匹配记录 = main()
