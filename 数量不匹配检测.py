#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数量不匹配检测程序 - 检测同一金额在银行和报表中的数量差异
"""

import pandas as pd
import numpy as np

def load_data():
    """加载数据"""
    print("=== 加载数据 ===")
    
    # 1. 报表数据
    报表数据 = pd.read_excel('7.04报表数.xlsx', sheet_name='Sheet1')
    报表数据['金额'] = pd.to_numeric(报表数据['fkje'], errors='coerce')
    报表数据 = 报表数据.dropna(subset=['金额'])
    print(f"报表数据: {len(报表数据)}条")
    
    # 2. 农商行数据
    农商行原始 = pd.read_excel('农商行.xlsx', sheet_name='Sheet1')
    农商行数据 = 农商行原始.iloc[1:].reset_index(drop=True)
    农商行数据.columns = ['标识', '商户号', '营业名称', '交易时间', '终端号', '终端别名', 
                       '交易类型', '支付方式', '订单金额', '商户订单号', '支付流水号']
    农商行数据['金额'] = pd.to_numeric(农商行数据['订单金额'], errors='coerce')
    农商行数据 = 农商行数据.dropna(subset=['金额'])
    农商行数据 = 农商行数据[农商行数据['金额'] > 0]
    农商行数据['银行来源'] = '农商行'
    print(f"农商行数据: {len(农商行数据)}条")
    
    # 3. 江苏行数据
    江苏行数据 = pd.read_excel('江苏行.xlsx', sheet_name='江苏行')
    江苏行数据['金额'] = pd.to_numeric(江苏行数据['交易金额'], errors='coerce')
    江苏行数据 = 江苏行数据.dropna(subset=['金额'])
    江苏行正数 = 江苏行数据[江苏行数据['金额'] > 0].copy()
    江苏行正数['银行来源'] = '江苏行'
    print(f"江苏行数据(正数): {len(江苏行正数)}条")
    
    return 报表数据, 农商行数据, 江苏行正数

def detect_quantity_mismatch():
    """检测数量不匹配"""
    print("\n=== 数量不匹配检测 ===")
    
    报表数据, 农商行数据, 江苏行数据 = load_data()
    
    # 合并银行数据
    银行数据 = pd.concat([农商行数据, 江苏行数据], ignore_index=True)
    print(f"银行总数据: {len(银行数据)}条")
    
    # 统计各金额的数量
    报表金额统计 = 报表数据['金额'].value_counts().sort_index()
    银行金额统计 = 银行数据['金额'].value_counts().sort_index()
    农商行金额统计 = 农商行数据['金额'].value_counts().sort_index()
    江苏行金额统计 = 江苏行数据['金额'].value_counts().sort_index()
    
    print(f"报表不同金额种类: {len(报表金额统计)}")
    print(f"银行不同金额种类: {len(银行金额统计)}")
    
    # 找出所有涉及的金额
    所有金额 = set(报表金额统计.index) | set(银行金额统计.index)
    
    数量不匹配记录 = []
    完全匹配记录 = []
    仅在报表记录 = []
    仅在银行记录 = []
    
    for 金额 in sorted(所有金额):
        报表数量 = 报表金额统计.get(金额, 0)
        银行数量 = 银行金额统计.get(金额, 0)
        农商行数量 = 农商行金额统计.get(金额, 0)
        江苏行数量 = 江苏行金额统计.get(金额, 0)
        
        if 报表数量 > 0 and 银行数量 > 0:
            if 报表数量 != 银行数量:
                # 数量不匹配
                数量不匹配记录.append({
                    '金额': 金额,
                    '报表数量': 报表数量,
                    '银行总数量': 银行数量,
                    '农商行数量': 农商行数量,
                    '江苏行数量': 江苏行数量,
                    '差额': 报表数量 - 银行数量,
                    '差额类型': '报表多' if 报表数量 > 银行数量 else '银行多'
                })
            else:
                # 完全匹配
                完全匹配记录.append({
                    '金额': 金额,
                    '数量': 报表数量
                })
        elif 报表数量 > 0 and 银行数量 == 0:
            # 仅在报表中
            仅在报表记录.append({
                '金额': 金额,
                '报表数量': 报表数量
            })
        elif 报表数量 == 0 and 银行数量 > 0:
            # 仅在银行中
            仅在银行记录.append({
                '金额': 金额,
                '银行数量': 银行数量,
                '农商行数量': 农商行数量,
                '江苏行数量': 江苏行数量
            })
    
    # 输出结果
    print(f"\n=== 检测结果统计 ===")
    print(f"完全匹配的金额: {len(完全匹配记录)}种")
    print(f"数量不匹配的金额: {len(数量不匹配记录)}种")
    print(f"仅在报表中的金额: {len(仅在报表记录)}种")
    print(f"仅在银行中的金额: {len(仅在银行记录)}种")
    
    # 详细显示数量不匹配的记录
    if 数量不匹配记录:
        print(f"\n=== 数量不匹配明细 ===")
        print(f"{'金额':>8} {'报表':>4} {'银行':>4} {'农商行':>6} {'江苏行':>6} {'差额':>4} {'类型':>6}")
        print("-" * 50)
        
        # 按差额绝对值排序，显示差异最大的
        数量不匹配记录_sorted = sorted(数量不匹配记录, key=lambda x: abs(x['差额']), reverse=True)
        
        for 记录 in 数量不匹配记录_sorted:
            print(f"{记录['金额']:>8.2f} {记录['报表数量']:>4} {记录['银行总数量']:>4} "
                  f"{记录['农商行数量']:>6} {记录['江苏行数量']:>6} {记录['差额']:>4} {记录['差额类型']:>6}")
    
    # 特别关注您提到的两个金额
    print(f"\n=== 特别关注的金额 ===")
    关注金额 = [3.6, 3.3]
    
    for 金额 in 关注金额:
        匹配记录 = [r for r in 数量不匹配记录 if r['金额'] == 金额]
        if 匹配记录:
            记录 = 匹配记录[0]
            print(f"金额 {金额}元:")
            print(f"  报表数量: {记录['报表数量']}")
            print(f"  银行总数量: {记录['银行总数量']} (农商行:{记录['农商行数量']}, 江苏行:{记录['江苏行数量']})")
            print(f"  差额: {记录['差额']} ({记录['差额类型']})")
            
            # 显示具体的银行记录
            银行该金额记录 = 银行数据[银行数据['金额'] == 金额]
            print(f"  银行记录明细:")
            for idx, 银行记录 in 银行该金额记录.iterrows():
                print(f"    {银行记录['银行来源']}: {银行记录.get('商户订单号', '')}")
            
            # 显示具体的报表记录
            报表该金额记录 = 报表数据[报表数据['金额'] == 金额]
            print(f"  报表记录明细:")
            for idx, 报表记录 in 报表该金额记录.iterrows():
                print(f"    病人: {报表记录.get('brxm', '')}")
        else:
            print(f"金额 {金额}元: 数量完全匹配或不存在")
    
    # 分析不匹配的原因
    print(f"\n=== 不匹配原因分析 ===")
    银行多的记录 = [r for r in 数量不匹配记录 if r['差额类型'] == '银行多']
    报表多的记录 = [r for r in 数量不匹配记录 if r['差额类型'] == '报表多']
    
    print(f"银行记录多于报表: {len(银行多的记录)}种金额")
    print(f"报表记录多于银行: {len(报表多的记录)}种金额")
    
    if 银行多的记录:
        银行多总差额 = sum([r['差额'] for r in 银行多的记录])
        print(f"银行多出记录总数: {abs(银行多总差额)}条")
    
    if 报表多的记录:
        报表多总差额 = sum([r['差额'] for r in 报表多的记录])
        print(f"报表多出记录总数: {报表多总差额}条")
    
    # 保存详细报告
    print(f"\n正在保存数量不匹配检测报告...")
    with pd.ExcelWriter('数量不匹配检测报告.xlsx', engine='openpyxl') as writer:
        if 数量不匹配记录:
            pd.DataFrame(数量不匹配记录).to_excel(writer, sheet_name='数量不匹配', index=False)
        
        if 完全匹配记录:
            pd.DataFrame(完全匹配记录).to_excel(writer, sheet_name='完全匹配', index=False)
        
        if 仅在报表记录:
            pd.DataFrame(仅在报表记录).to_excel(writer, sheet_name='仅在报表', index=False)
        
        if 仅在银行记录:
            pd.DataFrame(仅在银行记录).to_excel(writer, sheet_name='仅在银行', index=False)
        
        # 汇总统计
        汇总数据 = pd.DataFrame({
            '类型': ['完全匹配', '数量不匹配', '仅在报表', '仅在银行'],
            '金额种类数': [len(完全匹配记录), len(数量不匹配记录), len(仅在报表记录), len(仅在银行记录)]
        })
        汇总数据.to_excel(writer, sheet_name='汇总统计', index=False)
    
    print("数量不匹配检测报告已保存为 '数量不匹配检测报告.xlsx'")
    
    return 数量不匹配记录, 完全匹配记录, 仅在报表记录, 仅在银行记录

def main():
    try:
        数量不匹配, 完全匹配, 仅在报表, 仅在银行 = detect_quantity_mismatch()
        
        print(f"\n=== 最终总结 ===")
        print(f"这种检测方法更准确地反映了实际的对账问题:")
        print(f"- 不是简单的'有'或'没有'")
        print(f"- 而是'数量是否一致'")
        print(f"- 能够精确定位每个金额的数量差异")
        print(f"- 有助于发现数据录入错误、重复交易、遗漏交易等问题")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
