#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版对账程序
"""

import pandas as pd
import numpy as np

def main():
    try:
        print("开始对账程序...")
        
        # 1. 读取报表数据
        print("读取报表数据...")
        报表数据 = pd.read_excel('7.04报表数.xlsx', sheet_name='Sheet1')
        print(f"报表数据行数: {len(报表数据)}")
        print(f"报表数据列名: {list(报表数据.columns)}")
        
        # 提取报表金额
        if 'fkje' in 报表数据.columns:
            报表数据['金额'] = pd.to_numeric(报表数据['fkje'], errors='coerce')
            报表总金额 = 报表数据['金额'].sum()
            print(f"报表总金额: {报表总金额:,.2f}")
        else:
            print("报表数据中未找到金额列")
            return
        
        # 2. 读取农商行数据
        print("\n读取农商行数据...")
        农商行原始 = pd.read_excel('农商行.xlsx', sheet_name='Sheet1')
        print(f"农商行原始数据行数: {len(农商行原始)}")
        
        # 找到农商行数据的实际开始行
        农商行数据 = None
        for i in range(len(农商行原始)):
            row_str = str(农商行原始.iloc[i].values)
            if '商户订单号' in row_str or '订单金额' in row_str:
                print(f"找到农商行数据头部在第{i+1}行")
                农商行数据 = 农商行原始.iloc[i+1:].reset_index(drop=True)
                # 设置列名
                if len(农商行数据.columns) >= 9:
                    农商行数据.columns = ['商户号', '营业名称', '交易时间', '终端号', '终端别名', 
                                       '交易类型', '支付方式', '订单金额', '商户订单号'] + \
                                      [f'列{j}' for j in range(9, len(农商行数据.columns))]
                break
        
        if 农商行数据 is not None:
            # 清理农商行金额数据
            农商行数据['金额'] = pd.to_numeric(农商行数据['订单金额'], errors='coerce')
            农商行数据 = 农商行数据.dropna(subset=['金额'])
            农商行总金额 = 农商行数据['金额'].sum()
            print(f"农商行有效数据行数: {len(农商行数据)}")
            print(f"农商行总金额: {农商行总金额:,.2f}")
        else:
            print("未能解析农商行数据")
            农商行总金额 = 0
        
        # 3. 读取江苏行数据
        print("\n读取江苏行数据...")
        江苏行数据 = pd.read_excel('江苏行.xlsx', sheet_name='江苏行')
        print(f"江苏行数据行数: {len(江苏行数据)}")
        print(f"江苏行数据列名: {list(江苏行数据.columns)}")
        
        if '交易金额' in 江苏行数据.columns:
            江苏行数据['金额'] = pd.to_numeric(江苏行数据['交易金额'], errors='coerce')
            江苏行数据 = 江苏行数据.dropna(subset=['金额'])
            江苏行总金额 = 江苏行数据['金额'].sum()
            print(f"江苏行有效数据行数: {len(江苏行数据)}")
            print(f"江苏行总金额: {江苏行总金额:,.2f}")
        else:
            print("江苏行数据中未找到交易金额列")
            江苏行总金额 = 0
        
        # 4. 汇总对账结果
        银行总金额 = 农商行总金额 + 江苏行总金额
        差额 = 报表总金额 - 银行总金额
        
        print(f"\n=== 对账汇总 ===")
        print(f"报表总金额:    {报表总金额:>15,.2f}")
        print(f"农商行总金额:  {农商行总金额:>15,.2f}")
        print(f"江苏行总金额:  {江苏行总金额:>15,.2f}")
        print(f"银行合计金额:  {银行总金额:>15,.2f}")
        print(f"差额:          {差额:>15,.2f}")
        
        if abs(差额) < 0.01:
            print("\n✅ 账目平衡！")
        else:
            print(f"\n❌ 存在差额: {差额:,.2f}")
            
            # 5. 分析不匹配的记录
            print("\n=== 详细分析 ===")
            
            # 按金额分组统计
            if 农商行数据 is not None and len(农商行数据) > 0:
                print("\n农商行金额分布:")
                农商行金额分布 = 农商行数据['金额'].value_counts().sort_index()
                for amount, count in 农商行金额分布.head(10).items():
                    print(f"  {amount:>8.2f}: {count}笔")
            
            print("\n江苏行金额分布:")
            江苏行金额分布 = 江苏行数据['金额'].value_counts().sort_index()
            for amount, count in 江苏行金额分布.head(10).items():
                print(f"  {amount:>8.2f}: {count}笔")
            
            print("\n报表金额分布:")
            报表金额分布 = 报表数据['金额'].value_counts().sort_index()
            for amount, count in 报表金额分布.head(10).items():
                print(f"  {amount:>8.2f}: {count}笔")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
