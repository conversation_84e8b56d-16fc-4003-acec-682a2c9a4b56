#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修正版对账程序 - 精确识别真正的银行单边数据
重点修正：严格的订单号匹配和数量检测
"""

import pandas as pd
import numpy as np
from itertools import combinations

def load_data():
    """加载数据"""
    print("=== 加载数据 ===")
    
    # 1. 报表数据
    报表数据 = pd.read_excel('7.04报表数.xlsx', sheet_name='Sheet1')
    报表数据['金额'] = pd.to_numeric(报表数据['fkje'], errors='coerce')
    报表数据 = 报表数据.dropna(subset=['金额'])
    报表数据['已匹配'] = False
    报表数据['匹配类型'] = ''
    报表数据['匹配详情'] = ''
    报表数据['匹配银行订单号'] = ''
    print(f"报表数据: {len(报表数据)}条")
    
    # 2. 农商行数据
    农商行原始 = pd.read_excel('农商行.xlsx', sheet_name='Sheet1')
    农商行数据 = 农商行原始.iloc[1:].reset_index(drop=True)
    农商行数据.columns = ['标识', '商户号', '营业名称', '交易时间', '终端号', '终端别名', 
                       '交易类型', '支付方式', '订单金额', '商户订单号', '支付流水号']
    农商行数据['金额'] = pd.to_numeric(农商行数据['订单金额'], errors='coerce')
    农商行数据 = 农商行数据.dropna(subset=['金额'])
    农商行数据 = 农商行数据[农商行数据['金额'] > 0]
    农商行数据['银行来源'] = '农商行'
    农商行数据['已匹配'] = False
    农商行数据['匹配报表记录'] = ''
    print(f"农商行数据: {len(农商行数据)}条")
    
    # 3. 江苏行数据
    江苏行数据 = pd.read_excel('江苏行.xlsx', sheet_name='江苏行')
    江苏行数据['金额'] = pd.to_numeric(江苏行数据['交易金额'], errors='coerce')
    江苏行数据 = 江苏行数据.dropna(subset=['金额'])
    江苏行正数 = 江苏行数据[江苏行数据['金额'] > 0].copy()
    江苏行正数['银行来源'] = '江苏行'
    江苏行正数['已匹配'] = False
    江苏行正数['匹配报表记录'] = ''
    print(f"江苏行数据(正数): {len(江苏行正数)}条")
    
    return 报表数据, 农商行数据, 江苏行正数

def layer1_strict_order_match(报表数据, 银行数据):
    """第一层：严格订单号匹配"""
    print("\n=== 第一层：严格订单号匹配 ===")
    
    匹配结果 = []
    
    for 银行idx, 银行记录 in 银行数据.iterrows():
        if 银行记录['已匹配']:
            continue
            
        银行订单号 = str(银行记录.get('商户订单号', ''))
        if not 银行订单号 or 银行订单号 == 'nan' or 银行订单号 == '':
            continue
        
        # 在报表的yhddh字段中查找精确匹配
        报表匹配 = 报表数据[
            (报表数据['已匹配'] == False) & 
            (报表数据['yhddh'].astype(str).str.contains(银行订单号, na=False, regex=False))
        ]
        
        if len(报表匹配) > 0:
            报表金额总和 = 报表匹配['金额'].sum()
            银行金额 = 银行记录['金额']
            
            if abs(报表金额总和 - 银行金额) < 0.01:
                # 记录匹配详情
                病人列表 = 报表匹配['brxm'].tolist()
                金额列表 = 报表匹配['金额'].tolist()
                
                # 标记报表记录
                for 报表idx in 报表匹配.index:
                    报表数据.loc[报表idx, '已匹配'] = True
                    报表数据.loc[报表idx, '匹配类型'] = '订单号精确匹配'
                    报表数据.loc[报表idx, '匹配详情'] = f"银行订单号:{银行订单号}"
                    报表数据.loc[报表idx, '匹配银行订单号'] = 银行订单号
                
                # 标记银行记录
                银行数据.loc[银行idx, '已匹配'] = True
                银行数据.loc[银行idx, '匹配报表记录'] = f"病人:{','.join(病人列表)}"
                
                匹配结果.append({
                    '匹配类型': '订单号精确匹配',
                    '银行金额': 银行金额,
                    '报表金额': 报表金额总和,
                    '银行来源': 银行记录['银行来源'],
                    '银行订单号': 银行订单号,
                    '报表记录数': len(报表匹配),
                    '病人姓名': ','.join(病人列表),
                    '是否合并支付': '是' if len(报表匹配) > 1 else '否',
                    '金额组合': '+'.join([str(x) for x in 金额列表]) if len(报表匹配) > 1 else str(金额列表[0])
                })
    
    print(f"订单号精确匹配成功: {len(匹配结果)}笔")
    return 匹配结果

def layer2_strict_amount_match(报表数据, 银行数据):
    """第二层：严格金额匹配（一对一）"""
    print("\n=== 第二层：严格金额匹配 ===")
    
    匹配结果 = []
    
    # 获取未匹配的记录
    未匹配银行 = 银行数据[银行数据['已匹配'] == False]
    未匹配报表 = 报表数据[报表数据['已匹配'] == False]
    
    for 银行idx, 银行记录 in 未匹配银行.iterrows():
        银行金额 = 银行记录['金额']
        
        # 查找相同金额的未匹配报表记录
        相同金额报表 = 未匹配报表[未匹配报表['金额'] == 银行金额]
        相同金额未匹配 = 相同金额报表[相同金额报表['已匹配'] == False]
        
        if len(相同金额未匹配) > 0:
            # 取第一个匹配的记录
            报表记录 = 相同金额未匹配.iloc[0]
            
            # 标记为已匹配
            报表数据.loc[报表记录.name, '已匹配'] = True
            报表数据.loc[报表记录.name, '匹配类型'] = '金额直接匹配'
            报表数据.loc[报表记录.name, '匹配详情'] = f"金额:{银行金额}"
            
            银行数据.loc[银行idx, '已匹配'] = True
            银行数据.loc[银行idx, '匹配报表记录'] = f"病人:{报表记录.get('brxm', '')}"
            
            匹配结果.append({
                '匹配类型': '金额直接匹配',
                '银行金额': 银行金额,
                '报表金额': 报表记录['金额'],
                '银行来源': 银行记录['银行来源'],
                '银行订单号': 银行记录.get('商户订单号', ''),
                '病人姓名': 报表记录.get('brxm', '')
            })
    
    print(f"金额直接匹配成功: {len(匹配结果)}笔")
    return 匹配结果

def layer3_combination_match(报表数据, 银行数据):
    """第三层：组合匹配（合并支付和净额匹配）"""
    print("\n=== 第三层：组合匹配 ===")
    
    匹配结果 = []
    
    # 获取未匹配的记录
    未匹配银行 = 银行数据[银行数据['已匹配'] == False]
    未匹配报表 = 报表数据[报表数据['已匹配'] == False]
    
    # 按病人分组进行组合匹配
    for 病人姓名, 病人记录组 in 未匹配报表.groupby('brxm'):
        病人未匹配记录 = 病人记录组[病人记录组['已匹配'] == False]
        
        if len(病人未匹配记录) < 2:
            continue
        
        金额列表 = 病人未匹配记录['金额'].tolist()
        
        # 对每个未匹配的银行记录尝试组合匹配
        for 银行idx, 银行记录 in 未匹配银行.iterrows():
            if 银行记录['已匹配']:
                continue
                
            银行金额 = 银行记录['金额']
            
            # 尝试2-5个金额的组合
            for 组合数 in range(2, min(6, len(金额列表) + 1)):
                找到匹配 = False
                for 组合 in combinations(金额列表, 组合数):
                    组合总额 = sum(组合)
                    
                    if abs(组合总额 - 银行金额) < 0.01:
                        # 找到匹配组合，标记相关记录
                        组合记录索引 = []
                        剩余金额 = list(组合)
                        
                        for idx, 记录 in 病人未匹配记录.iterrows():
                            if not 记录['已匹配'] and 记录['金额'] in 剩余金额:
                                组合记录索引.append(idx)
                                剩余金额.remove(记录['金额'])
                                if len(剩余金额) == 0:
                                    break
                        
                        if len(组合记录索引) == len(组合):
                            # 判断匹配类型
                            负数金额 = [x for x in 组合 if x < 0]
                            正数金额 = [x for x in 组合 if x > 0]
                            
                            if len(负数金额) > 0:
                                匹配类型 = '净额匹配（含退款）'
                            else:
                                匹配类型 = '合并支付匹配'
                            
                            # 标记为已匹配
                            for 记录idx in 组合记录索引:
                                报表数据.loc[记录idx, '已匹配'] = True
                                报表数据.loc[记录idx, '匹配类型'] = 匹配类型
                                报表数据.loc[记录idx, '匹配详情'] = f"病人:{病人姓名},组合:{'+'.join([str(x) for x in 组合])}"
                            
                            银行数据.loc[银行idx, '已匹配'] = True
                            银行数据.loc[银行idx, '匹配报表记录'] = f"病人:{病人姓名},组合匹配"
                            
                            匹配结果.append({
                                '匹配类型': 匹配类型,
                                '银行金额': 银行金额,
                                '报表组合': 组合,
                                '银行来源': 银行记录['银行来源'],
                                '银行订单号': 银行记录.get('商户订单号', ''),
                                '病人姓名': 病人姓名,
                                '组合明细': f"{'+'.join([str(x) for x in 组合])}",
                                '组合数量': len(组合),
                                '包含负数': len(负数金额) > 0
                            })
                            
                            找到匹配 = True
                            break
                    if 找到匹配:
                        break
                if 找到匹配:
                    break
            if 银行记录['已匹配']:
                break
    
    print(f"组合匹配成功: {len(匹配结果)}笔")
    return 匹配结果

def analyze_true_unmatched(报表数据, 银行数据):
    """分析真正的未匹配记录"""
    print("\n=== 分析真正的未匹配记录 ===")
    
    未匹配报表 = 报表数据[报表数据['已匹配'] == False]
    未匹配银行 = 银行数据[银行数据['已匹配'] == False]
    
    print(f"真正未匹配报表记录: {len(未匹配报表)}条")
    print(f"真正未匹配银行记录: {len(未匹配银行)}条")
    
    # 重点分析您提到的两笔记录
    特殊关注记录 = [
        'S202507041353301001077384',  # 3.6元
        'S202507041610531001121128'   # 3.3元
    ]
    
    print(f"\n=== 特殊关注记录分析 ===")
    for 订单号 in 特殊关注记录:
        银行记录 = 银行数据[银行数据['商户订单号'].astype(str).str.contains(订单号, na=False)]
        
        if len(银行记录) > 0:
            记录 = 银行记录.iloc[0]
            匹配状态 = "已匹配" if 记录['已匹配'] else "未匹配"
            
            print(f"订单号 {订单号}:")
            print(f"  金额: {记录['金额']}元")
            print(f"  银行来源: {记录['银行来源']}")
            print(f"  匹配状态: {匹配状态}")
            
            if 记录['已匹配']:
                print(f"  匹配详情: {记录.get('匹配报表记录', '')}")
            else:
                print(f"  ⚠️  这是真正的银行单边数据！")
                
                # 检查报表中是否有相同金额
                相同金额报表 = 报表数据[报表数据['金额'] == 记录['金额']]
                print(f"  报表中相同金额记录数: {len(相同金额报表)}条")
                
                if len(相同金额报表) > 0:
                    已匹配数 = len(相同金额报表[相同金额报表['已匹配'] == True])
                    未匹配数 = len(相同金额报表[相同金额报表['已匹配'] == False])
                    print(f"    已匹配: {已匹配数}条, 未匹配: {未匹配数}条")
                    
                    if 未匹配数 > 0:
                        print(f"    未匹配报表记录:")
                        for idx, 报表记录 in 相同金额报表[相同金额报表['已匹配'] == False].iterrows():
                            print(f"      病人: {报表记录.get('brxm', '')}, 订单号: {报表记录.get('yhddh', '')}")
        else:
            print(f"订单号 {订单号}: 未找到对应银行记录")
    
    return 未匹配报表, 未匹配银行

def main():
    """主函数"""
    try:
        print("=== 修正版对账程序 ===")
        
        # 加载数据
        报表数据, 农商行数据, 江苏行数据 = load_data()
        银行数据 = pd.concat([农商行数据, 江苏行数据], ignore_index=True)
        print(f"银行总数据: {len(银行数据)}条")
        
        # 三层匹配
        订单号匹配结果 = layer1_strict_order_match(报表数据, 银行数据)
        金额匹配结果 = layer2_strict_amount_match(报表数据, 银行数据)
        组合匹配结果 = layer3_combination_match(报表数据, 银行数据)
        
        # 分析真正的未匹配记录
        未匹配报表, 未匹配银行 = analyze_true_unmatched(报表数据, 银行数据)
        
        # 统计结果
        总匹配银行数 = len(银行数据) - len(未匹配银行)
        匹配率 = 总匹配银行数 / len(银行数据) * 100
        
        print(f"\n=== 最终统计结果 ===")
        print(f"订单号精确匹配: {len(订单号匹配结果)}笔")
        print(f"金额直接匹配: {len(金额匹配结果)}笔")
        print(f"组合匹配: {len(组合匹配结果)}笔")
        print(f"银行记录总匹配率: {匹配率:.1f}%")
        print(f"真正未匹配银行记录: {len(未匹配银行)}条")
        print(f"真正未匹配报表记录: {len(未匹配报表)}条")
        
        # 保存详细报告
        print(f"\n正在生成修正版对账报告...")
        with pd.ExcelWriter('修正版对账报告.xlsx', engine='openpyxl') as writer:
            # 各层匹配结果
            if 订单号匹配结果:
                pd.DataFrame(订单号匹配结果).to_excel(writer, sheet_name='订单号精确匹配', index=False)
            if 金额匹配结果:
                pd.DataFrame(金额匹配结果).to_excel(writer, sheet_name='金额直接匹配', index=False)
            if 组合匹配结果:
                pd.DataFrame(组合匹配结果).to_excel(writer, sheet_name='组合匹配', index=False)
            
            # 真正的未匹配记录
            if len(未匹配报表) > 0:
                未匹配报表[['brxm', 'fkje', 'fkmc', 'yhddh', '匹配类型']].to_excel(
                    writer, sheet_name='真正未匹配报表', index=False)
            if len(未匹配银行) > 0:
                未匹配银行[['金额', '银行来源', '商户订单号', '交易时间', '匹配报表记录']].to_excel(
                    writer, sheet_name='真正未匹配银行', index=False)
            
            # 合并支付明细
            合并支付记录 = [r for r in 订单号匹配结果 if r['是否合并支付'] == '是']
            if 合并支付记录:
                pd.DataFrame(合并支付记录).to_excel(writer, sheet_name='合并支付明细', index=False)
        
        print("修正版对账报告已保存为 '修正版对账报告.xlsx'")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
