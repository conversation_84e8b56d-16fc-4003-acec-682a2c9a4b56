#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复版单边数据检测程序 - 修复之前的逻辑错误
"""

import pandas as pd
import numpy as np
from itertools import combinations

def load_data_carefully():
    """仔细加载和清理数据"""
    print("=== 仔细加载数据 ===")
    
    # 1. 报表数据
    报表数据 = pd.read_excel('7.04报表数.xlsx', sheet_name='Sheet1')
    报表数据['金额'] = pd.to_numeric(报表数据['fkje'], errors='coerce')
    报表数据 = 报表数据.dropna(subset=['金额'])
    print(f"报表数据: {len(报表数据)}条")
    
    # 2. 农商行数据 - 只取正数金额
    农商行原始 = pd.read_excel('农商行.xlsx', sheet_name='Sheet1')
    农商行数据 = 农商行原始.iloc[1:].reset_index(drop=True)
    农商行数据.columns = ['标识', '商户号', '营业名称', '交易时间', '终端号', '终端别名', 
                       '交易类型', '支付方式', '订单金额', '商户订单号', '支付流水号']
    农商行数据['金额'] = pd.to_numeric(农商行数据['订单金额'], errors='coerce')
    农商行数据 = 农商行数据.dropna(subset=['金额'])
    农商行数据 = 农商行数据[农商行数据['金额'] > 0]  # 只保留正数
    农商行数据['银行来源'] = '农商行'
    print(f"农商行数据(正数): {len(农商行数据)}条")
    
    # 3. 江苏行数据 - 只取正数金额
    江苏行原始 = pd.read_excel('江苏行.xlsx', sheet_name='江苏行')
    江苏行数据 = 江苏行原始.copy()
    江苏行数据['金额'] = pd.to_numeric(江苏行数据['交易金额'], errors='coerce')
    江苏行数据 = 江苏行数据.dropna(subset=['金额'])
    江苏行正数 = 江苏行数据[江苏行数据['金额'] > 0]  # 只保留正数
    江苏行正数['银行来源'] = '江苏行'
    print(f"江苏行数据(正数): {len(江苏行正数)}条")
    
    return 报表数据, 农商行数据, 江苏行正数

def check_specific_records():
    """检查特定的单边记录"""
    print("\n=== 检查特定单边记录 ===")
    
    报表数据, 农商行数据, 江苏行数据 = load_data_carefully()
    
    # 您提到的两笔单边记录
    单边记录 = [
        {'金额': 3.6, '流水号': 'S202507041353301001077384'},
        {'金额': 3.3, '流水号': 'S202507041610531001121128'}
    ]
    
    for 记录 in 单边记录:
        金额 = 记录['金额']
        流水号 = 记录['流水号']
        
        print(f"\n--- 检查记录: {金额}元, 流水号: {流水号} ---")
        
        # 1. 在银行数据中查找
        在农商行 = 农商行数据[
            (农商行数据['金额'] == 金额) | 
            (农商行数据['商户订单号'].str.contains(流水号, na=False)) |
            (农商行数据['支付流水号'].str.contains(流水号, na=False))
        ]
        
        在江苏行 = 江苏行数据[
            (江苏行数据['金额'] == 金额) | 
            (江苏行数据['商户订单号'].str.contains(流水号, na=False)) |
            (江苏行数据['银商订单号'].str.contains(流水号, na=False))
        ]
        
        if len(在农商行) > 0:
            print(f"  ✓ 在农商行数据中找到:")
            for idx, 银行记录 in 在农商行.iterrows():
                print(f"    金额: {银行记录['金额']}, 订单号: {银行记录['商户订单号']}")
        
        if len(在江苏行) > 0:
            print(f"  ✓ 在江苏行数据中找到:")
            for idx, 银行记录 in 在江苏行.iterrows():
                print(f"    金额: {银行记录['金额']}, 订单号: {银行记录.get('商户订单号', '')}")
        
        if len(在农商行) == 0 and len(在江苏行) == 0:
            print(f"  ❌ 在银行数据中未找到此记录")
            continue
        
        # 2. 在报表数据中查找直接匹配
        报表直接匹配 = 报表数据[报表数据['金额'] == 金额]
        if len(报表直接匹配) > 0:
            print(f"  ✓ 在报表中找到直接金额匹配 {len(报表直接匹配)} 条:")
            for idx, 报表记录 in 报表直接匹配.iterrows():
                print(f"    病人: {报表记录.get('brxm', '')}, 金额: {报表记录['金额']}")
        else:
            print(f"  ❌ 在报表中未找到直接金额匹配")
        
        # 3. 查找组合匹配
        print(f"  检查组合匹配...")
        找到组合 = False
        
        for 病人姓名, 病人记录 in 报表数据.groupby('brxm'):
            金额列表 = 病人记录['金额'].tolist()
            
            # 检查2-5个金额的组合
            for 组合数 in range(2, min(6, len(金额列表) + 1)):
                for 组合 in combinations(金额列表, 组合数):
                    if abs(sum(组合) - 金额) < 0.01:
                        print(f"    ✓ 找到组合匹配 - 病人: {病人姓名}")
                        print(f"      组合: {' + '.join([str(x) for x in 组合])} = {sum(组合)}")
                        找到组合 = True
                        break
                if 找到组合:
                    break
            if 找到组合:
                break
        
        if not 找到组合:
            print(f"  ❌ 未找到组合匹配")
        
        # 4. 总结
        有任何匹配 = len(报表直接匹配) > 0 or 找到组合
        if not 有任何匹配:
            print(f"  🚨 确认为银行单边数据！")
        else:
            print(f"  ✅ 在报表中找到匹配")

def comprehensive_single_side_detection():
    """全面的单边数据检测"""
    print("\n=== 全面单边数据检测 ===")
    
    报表数据, 农商行数据, 江苏行数据 = load_data_carefully()
    
    # 合并银行数据
    银行数据 = pd.concat([农商行数据, 江苏行数据], ignore_index=True)
    print(f"银行总数据(正数): {len(银行数据)}条")
    
    真正单边记录 = []
    
    for idx, 银行记录 in 银行数据.iterrows():
        银行金额 = 银行记录['金额']
        银行来源 = 银行记录['银行来源']
        订单号 = 银行记录.get('商户订单号', '')
        
        # 1. 直接金额匹配
        直接匹配 = 报表数据[报表数据['金额'] == 银行金额]
        
        # 2. 组合匹配检查
        找到组合 = False
        if len(直接匹配) == 0:  # 只有在没有直接匹配时才检查组合
            for 病人姓名, 病人记录 in 报表数据.groupby('brxm'):
                金额列表 = 病人记录['金额'].tolist()
                
                for 组合数 in range(2, min(6, len(金额列表) + 1)):
                    for 组合 in combinations(金额列表, 组合数):
                        if abs(sum(组合) - 银行金额) < 0.01:
                            找到组合 = True
                            break
                    if 找到组合:
                        break
                if 找到组合:
                    break
        
        # 如果既没有直接匹配也没有组合匹配，则为单边数据
        if len(直接匹配) == 0 and not 找到组合:
            真正单边记录.append({
                '金额': 银行金额,
                '银行来源': 银行来源,
                '订单号': 订单号,
                '交易时间': 银行记录.get('交易时间', ''),
                '支付方式': 银行记录.get('支付方式', 银行记录.get('交易方式', ''))
            })
    
    print(f"\n=== 检测结果 ===")
    print(f"银行总记录: {len(银行数据)}条")
    print(f"真正单边记录: {len(真正单边记录)}条")
    print(f"匹配率: {((len(银行数据) - len(真正单边记录))/len(银行数据))*100:.1f}%")
    
    if 真正单边记录:
        print(f"\n真正的银行单边数据:")
        print(f"{'金额':>8} {'银行来源':>8} {'订单号':>25}")
        print("-" * 45)
        
        for 记录 in sorted(真正单边记录, key=lambda x: x['金额']):
            print(f"{记录['金额']:>8.2f} {记录['银行来源']:>8} {记录['订单号']:>25}")
    
    # 保存结果
    if 真正单边记录:
        with pd.ExcelWriter('修复版单边检测结果.xlsx', engine='openpyxl') as writer:
            pd.DataFrame(真正单边记录).to_excel(writer, sheet_name='银行单边数据', index=False)
        print(f"\n结果已保存到 '修复版单边检测结果.xlsx'")
    
    return 真正单边记录

def main():
    try:
        # 检查特定记录
        check_specific_records()
        
        # 全面检测
        单边记录 = comprehensive_single_side_detection()
        
        print(f"\n=== 总结 ===")
        if len(单边记录) > 0:
            print(f"发现 {len(单边记录)} 条真正的银行单边数据")
            print("这些记录在银行中存在但在报表中完全找不到对应关系")
        else:
            print("未发现银行单边数据")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
