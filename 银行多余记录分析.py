#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
银行多余记录分析程序 - 专门分析银行中有但报表中看似没有的记录
"""

import pandas as pd
import numpy as np
from itertools import combinations

def load_data():
    """加载数据"""
    print("加载数据...")
    
    # 报表数据
    报表数据 = pd.read_excel('7.04报表数.xlsx', sheet_name='Sheet1')
    报表数据['金额'] = pd.to_numeric(报表数据['fkje'], errors='coerce')
    报表数据 = 报表数据.dropna(subset=['金额'])
    
    # 农商行数据
    农商行原始 = pd.read_excel('农商行.xlsx', sheet_name='Sheet1')
    农商行数据 = 农商行原始.iloc[1:].reset_index(drop=True)
    农商行数据.columns = ['标识', '商户号', '营业名称', '交易时间', '终端号', '终端别名', 
                       '交易类型', '支付方式', '订单金额', '商户订单号', '支付流水号']
    农商行数据['金额'] = pd.to_numeric(农商行数据['订单金额'], errors='coerce')
    农商行数据 = 农商行数据.dropna(subset=['金额'])
    农商行数据 = 农商行数据[农商行数据['金额'] > 0]
    
    # 江苏行数据
    江苏行数据 = pd.read_excel('江苏行.xlsx', sheet_name='江苏行')
    江苏行数据['金额'] = pd.to_numeric(江苏行数据['交易金额'], errors='coerce')
    江苏行数据 = 江苏行数据.dropna(subset=['金额'])
    
    return 报表数据, 农商行数据, 江苏行数据

def analyze_specific_bank_records():
    """分析特定的银行多余记录"""
    
    # 您提到的银行多余记录
    银行多余记录 = [
        {'金额': 39.96, '订单号': 'S202507040921160220970593', '银行': '农商行'},
        {'金额': 52.86, '订单号': 'S202507040807400220930199', '银行': '农商行'},
        {'金额': 71.54, '订单号': 'S202507041054220220012351', '银行': '农商行'},
        {'金额': 370.38, '订单号': 'S202507041626230220126480', '银行': '农商行'},
        {'金额': 403.26, '订单号': 'S202507040820280220936625', '银行': '农商行'},
        {'金额': 782.08, '订单号': 'S202507040932020220974878', '银行': '农商行'}
    ]
    
    报表数据, 农商行数据, 江苏行数据 = load_data()
    
    print("=== 银行多余记录深度分析 ===")
    
    分析结果 = []
    
    for 银行记录 in 银行多余记录:
        银行金额 = 银行记录['金额']
        订单号 = 银行记录['订单号']
        
        print(f"\n分析银行记录: {银行金额}元 (订单号: {订单号})")
        
        # 1. 检查是否有完全相同的金额
        相同金额记录 = 报表数据[报表数据['金额'] == 银行金额]
        if len(相同金额记录) > 0:
            print(f"  ✓ 找到相同金额记录 {len(相同金额记录)} 条")
            for idx, 记录 in 相同金额记录.iterrows():
                print(f"    病人: {记录.get('brxm', '')}, 付款名称: {记录.get('fkmc', '')}")
        
        # 2. 寻找可能的组合匹配（正数金额组合）
        print(f"  寻找正数金额组合...")
        正数报表 = 报表数据[报表数据['金额'] > 0]
        找到组合 = False
        
        # 按病人分组寻找
        for 病人姓名, 病人记录 in 正数报表.groupby('brxm'):
            金额列表 = 病人记录['金额'].tolist()
            
            # 尝试2-5个金额的组合
            for 组合数 in range(2, min(6, len(金额列表) + 1)):
                for 组合 in combinations(金额列表, 组合数):
                    if abs(sum(组合) - 银行金额) < 0.01:
                        print(f"    ✓ 找到组合匹配 - 病人: {病人姓名}")
                        print(f"      组合: {' + '.join([str(x) for x in 组合])} = {sum(组合)}")
                        找到组合 = True
                        break
                if 找到组合:
                    break
            if 找到组合:
                break
        
        # 3. 寻找净额匹配（包含正负金额）
        print(f"  寻找净额匹配（包含退款）...")
        找到净额匹配 = False
        
        for 病人姓名, 病人记录 in 报表数据.groupby('brxm'):
            金额列表 = 病人记录['金额'].tolist()
            
            # 尝试包含正负金额的组合
            for 组合数 in range(2, min(6, len(金额列表) + 1)):
                for 组合 in combinations(金额列表, 组合数):
                    净额 = sum(组合)
                    if abs(净额 - 银行金额) < 0.01:
                        正数 = [x for x in 组合 if x > 0]
                        负数 = [x for x in 组合 if x < 0]
                        
                        if len(负数) > 0:  # 包含退款
                            print(f"    ✓ 找到净额匹配 - 病人: {病人姓名}")
                            print(f"      支付: {正数}")
                            print(f"      退款: {负数}")
                            print(f"      净额: {净额}")
                            找到净额匹配 = True
                            break
                if 找到净额匹配:
                    break
            if 找到净额匹配:
                break
        
        # 4. 寻找跨病人的组合
        print(f"  寻找跨病人组合...")
        所有正数金额 = 正数报表['金额'].tolist()
        找到跨病人组合 = False
        
        # 限制搜索范围，避免计算量过大
        if len(所有正数金额) <= 100:  # 只在数据量不大时进行跨病人搜索
            for 组合数 in range(2, min(4, len(所有正数金额) + 1)):
                组合计数 = 0
                for 组合 in combinations(所有正数金额, 组合数):
                    组合计数 += 1
                    if 组合计数 > 1000:  # 限制搜索次数
                        break
                    if abs(sum(组合) - 银行金额) < 0.01:
                        print(f"    ✓ 找到跨病人组合匹配")
                        print(f"      组合: {' + '.join([str(x) for x in 组合])} = {sum(组合)}")
                        找到跨病人组合 = True
                        break
                if 找到跨病人组合:
                    break
        
        # 记录分析结果
        分析结果.append({
            '银行金额': 银行金额,
            '订单号': 订单号,
            '有相同金额': len(相同金额记录) > 0,
            '找到组合匹配': 找到组合,
            '找到净额匹配': 找到净额匹配,
            '找到跨病人组合': 找到跨病人组合,
            '相同金额数量': len(相同金额记录)
        })
        
        if not (len(相同金额记录) > 0 or 找到组合 or 找到净额匹配 or 找到跨病人组合):
            print(f"    ❌ 未找到任何匹配")
    
    # 输出汇总结果
    print(f"\n=== 分析汇总 ===")
    for 结果 in 分析结果:
        状态 = "已找到匹配" if (结果['有相同金额'] or 结果['找到组合匹配'] or 
                           结果['找到净额匹配'] or 结果['找到跨病人组合']) else "未找到匹配"
        print(f"金额 {结果['银行金额']:>8.2f}: {状态}")
    
    # 保存分析报告
    print(f"\n正在保存银行多余记录分析报告...")
    with pd.ExcelWriter('银行多余记录分析.xlsx', engine='openpyxl') as writer:
        pd.DataFrame(分析结果).to_excel(writer, sheet_name='分析结果', index=False)
    
    print("分析报告已保存为 '银行多余记录分析.xlsx'")

def main():
    try:
        analyze_specific_bank_records()
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
