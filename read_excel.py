#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
读取Excel文件并分析数据量
"""

import pandas as pd
import os

def analyze_excel_file(file_path):
    """
    分析Excel文件的数据量
    
    Args:
        file_path: Excel文件路径
    
    Returns:
        None, 打印分析结果
    """
    if os.path.exists(file_path):
        try:
            # 读取Excel文件的所有工作表
            excel_file = pd.ExcelFile(file_path)
            print(f'\n文件: {file_path}')
            print(f'工作表数量: {len(excel_file.sheet_names)}')
            print(f'工作表名称: {excel_file.sheet_names}')
            
            total_rows = 0
            total_cols = 0
            
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                rows, cols = df.shape
                total_rows += rows
                total_cols = max(total_cols, cols)
                print(f'  工作表 "{sheet_name}": {rows} 行 × {cols} 列')
            
            print(f'总数据行数: {total_rows}')
            print(f'最大列数: {total_cols}')
            print('-' * 50)
            
        except Exception as e:
            print(f'读取文件 {file_path} 时出错: {e}')
    else:
        print(f'文件 {file_path} 不存在')

def main():
    """
    主函数
    """
    files = ['7.04报表数.xlsx', '农商行.xlsx', '江苏行.xlsx']
    
    for file in files:
        analyze_excel_file(file)

if __name__ == "__main__":
    main()
